# 🐍 PyPOTS Conda Environment 使用指南

## 🎉 环境创建成功！

根据之前的输出，我们已经成功创建了一个名为 `pypots` 的conda环境，并且PyPOTS已经正确安装。

## ✅ 环境状态确认

从创建过程的输出可以看到：
- ✅ PyPOTS环境已创建
- ✅ Python 3.9 已安装
- ✅ PyTorch (CPU版本) 已安装
- ✅ PyPOTS 已安装并可以导入
- ✅ TimeMixer 可以正常导入
- ✅ Adam optimizer 可以正常导入
- ✅ 显示了 ai4ts v0.0.3 标志，确认PyPOTS生态系统正常

## 🚀 如何使用新环境

### 方法1: 命令行激活
```bash
# 激活环境
conda activate pypots

# 运行TimeMixer++脚本
python timemixer_based_on_test.py

# 或者测试环境
python test_new_env.py
```

### 方法2: 一键运行
创建一个批处理文件 `run_timemixer.bat`：
```batch
@echo off
echo Activating PyPOTS environment and running TimeMixer++...
conda activate pypots
python timemixer_based_on_test.py
pause
```

### 方法3: 在IDE中使用
1. 在您的IDE (如PyCharm, VSCode) 中
2. 选择Python解释器
3. 选择conda环境: `pypots`
4. 解释器路径通常在: `D:\APPS\Anaconda\envs\pypots\python.exe`

## 📊 TimeMixer++ 配置

基于 `test.py` 的配置，我们的TimeMixer++设置为：

```python
# 数据配置
n_input_steps = 60      # 输入60天数据
n_pred_steps = 14       # 预测14天
n_features = 1          # 单变量 (runoff)

# 模型配置
n_layers = 2            # 2层网络
d_model = 64            # 模型维度
d_ffn = 128             # 前馈网络维度
top_k = 5               # Top-k参数
epochs = 50             # 训练轮数

# 优化器
optimizer = Adam(lr=1e-3)  # Adam优化器
device = 'cpu'             # CPU训练
```

## 📁 项目文件结构

```
univariate_timeseries_forecasting/
├── timemixer_based_on_test.py     # 主要的TimeMixer++实现
├── test_new_env.py                # 环境测试脚本
├── pypots_requirements.txt        # PyPOTS依赖列表
├── create_pypots_conda_env.bat    # 环境创建脚本
├── setup_pypots_environment.py   # Python环境设置脚本
└── PYPOTS_ENVIRONMENT_GUIDE.md    # 本指南
```

## 🎯 运行TimeMixer++预测

现在您可以直接运行TimeMixer++来预测runoff数据：

```bash
# 激活环境
conda activate pypots

# 运行预测
python timemixer_based_on_test.py
```

### 预期输出：
```
🌊 TIMEMIXER++ RUNOFF FORECASTING (Based on test.py)
======================================================================
✅ Data loaded successfully
   📊 Shape: (24836, 2)
   📅 Date range: 1950-01-01 to 2017-12-31
   💧 Runoff range: 179.0 - 21200.0

🔧 Preparing PyPOTS Dataset
   📥 Input steps: 60
   📤 Prediction steps: 14
   ✅ Created XXXX sequences

🤖 Creating TimeMixer Model (Based on test.py)
✅ PyPOTS imported successfully
✅ TimeMixer model created successfully

🚀 Training TimeMixer Model
[训练进度...]
✅ Training completed!

🔮 Making predictions...
✅ Predictions completed

📊 Creating TimeMixer Visualizations
✅ Visualization saved: timemixer_runoff_predictions.png

🎉 TIMEMIXER++ FORECASTING COMPLETED!
```

## 📈 生成的文件

成功运行后会生成：
- `timemixer_runoff_predictions.png` - 预测结果可视化
- `timemixer_predictions.csv` - 预测数据CSV
- `timemixer_summary.csv` - 统计摘要
- `timemixer_runoff_results/` - 模型保存目录

## 🔧 环境管理

### 查看环境信息
```bash
# 查看所有环境
conda env list

# 查看当前环境包
conda list

# 查看PyPOTS版本
python -c "import pypots; print(pypots.__version__)"
```

### 环境维护
```bash
# 更新PyPOTS
conda activate pypots
pip install --upgrade pypots

# 删除环境（如果需要重建）
conda env remove -n pypots
```

## 🐛 故障排除

### 问题1: 环境激活失败
```bash
# 重新初始化conda
conda init
# 重启终端后再试
```

### 问题2: PyPOTS导入失败
```bash
# 重新安装PyPOTS
conda activate pypots
pip uninstall pypots -y
pip install pypots
```

### 问题3: 训练时间过长
- 减少 `epochs` 数量
- 使用更小的 `d_model`
- 减少数据量进行测试

### 问题4: 内存不足
- 减小批次大小
- 使用CPU而不是GPU
- 减少序列长度

## 💡 使用建议

1. **首次运行**: 使用默认配置测试
2. **参数调优**: 根据结果调整模型参数
3. **数据预处理**: 检查数据质量和缺失值
4. **结果分析**: 查看生成的图表和指标
5. **模型保存**: 保存训练好的模型用于后续预测

## 🎊 总结

您现在拥有一个完全配置好的PyPOTS环境，可以：
- ✅ 运行TimeMixer++模型
- ✅ 处理您的runoff时间序列数据
- ✅ 生成14天的径流预测
- ✅ 可视化预测结果
- ✅ 保存和重用训练好的模型

**下一步**: 激活环境并运行 `python timemixer_based_on_test.py` 开始您的时间序列预测！
