"""
PyPOTS Environment Setup Script
===============================

This script creates a conda environment specifically configured for PyPOTS
based on the official requirements and best practices.
"""

import subprocess
import sys
import os

def run_command(command, description, check_success=True):
    """Run a command and handle errors."""
    print(f"🔧 {description}...")
    print(f"   Command: {command}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True,
            timeout=600  # 10 minute timeout
        )
        
        if result.returncode == 0:
            print(f"   ✅ Success")
            return True
        else:
            print(f"   ⚠️  Warning (return code {result.returncode})")
            if check_success:
                print(f"   Error: {result.stderr[:200]}...")
                return False
            return True
            
    except subprocess.TimeoutExpired:
        print(f"   ⏰ Timeout - command may still be running")
        return not check_success
    except Exception as e:
        print(f"   ❌ Exception: {str(e)[:100]}...")
        return not check_success

def create_pypots_environment():
    """Create PyPOTS conda environment with all dependencies."""
    print("🐍 CREATING PYPOTS CONDA ENVIRONMENT")
    print("=" * 60)
    print("Based on PyPOTS official requirements and dependencies")
    print()
    
    # Step 1: Remove existing environment
    print("STEP 1: Cleaning up existing environment")
    print("-" * 40)
    run_command("conda env remove -n pypots -y", "Removing existing pypots environment", check_success=False)
    
    # Step 2: Create new environment
    print("\nSTEP 2: Creating new conda environment")
    print("-" * 40)
    if not run_command("conda create -n pypots python=3.9 -y", "Creating pypots environment with Python 3.9"):
        print("❌ Failed to create conda environment")
        return False
    
    # Step 3: Install conda packages
    print("\nSTEP 3: Installing core packages via conda")
    print("-" * 40)
    
    conda_packages = [
        "numpy>=1.20.0",
        "pandas>=1.3.0", 
        "matplotlib>=3.5.0",
        "scikit-learn>=1.0.0",
        "scipy>=1.7.0",
        "jupyter",
        "notebook",
        "ipykernel"
    ]
    
    conda_cmd = f"conda install -n pypots -c conda-forge {' '.join(conda_packages)} -y"
    if not run_command(conda_cmd, "Installing core scientific packages"):
        print("⚠️ Some conda packages may have failed, continuing...")
    
    # Step 4: Install PyTorch
    print("\nSTEP 4: Installing PyTorch")
    print("-" * 40)
    
    # Install CPU version for maximum compatibility
    pytorch_cmd = "conda install -n pypots pytorch torchvision torchaudio cpuonly -c pytorch -y"
    if not run_command(pytorch_cmd, "Installing PyTorch (CPU version)"):
        print("❌ PyTorch installation failed")
        return False
    
    # Step 5: Install pip packages
    print("\nSTEP 5: Installing pip packages")
    print("-" * 40)
    
    pip_packages = [
        "packaging>=20.0",
        "setuptools>=50.0", 
        "wheel>=0.37.0",
        "transformers>=4.0.0",
        "tokenizers>=0.10.0",
        "datasets>=2.0.0",
        "h5py>=3.1.0",
        "openpyxl>=3.0.0",
        "seaborn>=0.11.0",
        "plotly>=5.0.0"
    ]
    
    # Activate environment and install pip packages
    activate_and_pip = f"conda activate pypots && pip install {' '.join(pip_packages)}"
    run_command(activate_and_pip, "Installing pip dependencies", check_success=False)
    
    # Step 6: Install PyPOTS
    print("\nSTEP 6: Installing PyPOTS")
    print("-" * 40)
    
    pypots_cmd = "conda activate pypots && pip install pypots"
    if not run_command(pypots_cmd, "Installing PyPOTS"):
        print("❌ PyPOTS installation failed")
        return False
    
    return True

def test_pypots_installation():
    """Test if PyPOTS installation is working."""
    print("\nSTEP 7: Testing PyPOTS Installation")
    print("-" * 40)
    
    test_commands = [
        ("conda activate pypots && python -c \"import pypots; print('PyPOTS version:', pypots.__version__)\"", 
         "Testing PyPOTS import"),
        ("conda activate pypots && python -c \"from pypots.forecasting import TimeMixer; print('TimeMixer imported')\"", 
         "Testing TimeMixer import"),
        ("conda activate pypots && python -c \"from pypots.optim import Adam; print('Adam optimizer imported')\"", 
         "Testing optimizer import"),
        ("conda activate pypots && python -c \"import torch; print('PyTorch version:', torch.__version__)\"", 
         "Testing PyTorch"),
        ("conda activate pypots && python -c \"import pandas as pd; import numpy as np; print('Core packages OK')\"", 
         "Testing core packages")
    ]
    
    all_passed = True
    for cmd, desc in test_commands:
        if not run_command(cmd, desc, check_success=False):
            all_passed = False
    
    return all_passed

def create_activation_script():
    """Create a script to easily activate the environment."""
    print("\nSTEP 8: Creating activation scripts")
    print("-" * 40)
    
    # Windows batch file
    batch_content = """@echo off
echo Activating PyPOTS environment...
call conda activate pypots
echo.
echo PyPOTS environment activated!
echo You can now run:
echo   python timemixer_based_on_test.py
echo.
cmd /k
"""
    
    with open("activate_pypots.bat", "w") as f:
        f.write(batch_content)
    
    # Python test script
    test_content = """#!/usr/bin/env python
# Test script for PyPOTS environment

print("Testing PyPOTS environment...")

try:
    import pypots
    print(f"✅ PyPOTS version: {pypots.__version__}")
    
    from pypots.forecasting import TimeMixer
    print("✅ TimeMixer imported")
    
    from pypots.optim import Adam
    print("✅ Adam optimizer imported")
    
    import torch
    print(f"✅ PyTorch version: {torch.__version__}")
    
    import pandas as pd
    import numpy as np
    print("✅ Core packages working")
    
    print("\\n🎉 PyPOTS environment is ready!")
    print("You can now run: python timemixer_based_on_test.py")
    
except Exception as e:
    print(f"❌ Error: {e}")
    print("Please check the environment setup.")
"""
    
    with open("test_pypots_env.py", "w") as f:
        f.write(test_content)
    
    print("✅ Created activation scripts:")
    print("   - activate_pypots.bat (Windows)")
    print("   - test_pypots_env.py (Test script)")

def main():
    """Main function to set up PyPOTS environment."""
    print("🐍 PYPOTS CONDA ENVIRONMENT SETUP")
    print("=" * 70)
    print("This script will create a complete PyPOTS environment")
    print("with all required dependencies for TimeMixer++ forecasting.")
    print()
    
    # Check if conda is available
    try:
        result = subprocess.run("conda --version", shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ Conda is not available. Please install Anaconda or Miniconda first.")
            return
        print(f"✅ Found conda: {result.stdout.strip()}")
    except:
        print("❌ Conda is not available. Please install Anaconda or Miniconda first.")
        return
    
    # Confirm with user
    response = input("\nProceed with PyPOTS environment creation? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("Environment creation cancelled.")
        return
    
    # Create environment
    success = create_pypots_environment()
    
    if success:
        # Test installation
        test_success = test_pypots_installation()
        
        # Create helper scripts
        create_activation_script()
        
        # Final summary
        print("\n" + "=" * 70)
        print("🎉 PYPOTS ENVIRONMENT SETUP COMPLETED!")
        print("=" * 70)
        
        if test_success:
            print("✅ All tests passed! PyPOTS is ready to use.")
        else:
            print("⚠️ Some tests failed, but basic installation completed.")
        
        print("\n📋 Next steps:")
        print("1. Double-click 'activate_pypots.bat' to activate environment")
        print("2. Or manually run: conda activate pypots")
        print("3. Test with: python test_pypots_env.py")
        print("4. Run forecasting: python timemixer_based_on_test.py")
        
        print("\n📁 Environment details:")
        print("   - Name: pypots")
        print("   - Python: 3.9")
        print("   - PyTorch: CPU version (stable)")
        print("   - PyPOTS: Latest version")
        print("   - Location: Use 'conda env list' to see path")
        
    else:
        print("\n❌ Environment setup failed!")
        print("Please check the error messages above and try again.")
        print("\n💡 Troubleshooting:")
        print("1. Make sure conda is properly installed")
        print("2. Check your internet connection")
        print("3. Try running as administrator")

if __name__ == "__main__":
    main()
