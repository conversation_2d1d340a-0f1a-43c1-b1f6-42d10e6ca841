"""
基于LightGBM的特征重要性分析
============================

使用LightGBM分析所有变量与RUNOFF的重要性，
选择最相关的5个特征作为TimeMixer的输入特征。
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
import json
from pathlib import Path

class LightGBMFeatureSelector:
    def __init__(self, data_file='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        self.data_file = data_file
        self.all_features = ['RUNOFF', 'TEMP', 'DEWP', 'SLP', 'VISIB', 'WDSP', 'MXSPD', 'MAX', 'MIN', 'PRCP']
        self.target_feature = 'RUNOFF'
        self.df = None
        self.feature_importance = None
        self.selected_features = None
        self.lgb_model = None
        
    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("🔍 加载数据进行特征重要性分析")
        print("="*50)
        
        try:
            self.df = pd.read_csv(self.data_file)
            self.df['DATE'] = pd.to_datetime(self.df['DATE'], format='%Y/%m/%d')
            self.df = self.df.set_index('DATE')
            
            print(f"✅ 数据加载成功: {self.df.shape}")
            print(f"📅 时间范围: {self.df.index.min()} 到 {self.df.index.max()}")
            
            # 处理缺失值
            missing_before = self.df.isnull().sum().sum()
            if missing_before > 0:
                print(f"⚠️ 处理 {missing_before} 个缺失值...")
                for col in self.all_features:
                    if self.df[col].isnull().any():
                        mean_val = self.df[col].mean()
                        self.df[col].fillna(mean_val, inplace=True)
                print("✅ 缺失值处理完成")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def create_time_series_features(self, n_lags=30):
        """创建时间序列特征用于LightGBM分析"""
        print(f"\n🔧 创建时间序列特征 (滞后{n_lags}天)")
        print("="*40)
        
        # 为每个特征创建滞后特征
        feature_data = []
        target_data = []
        
        for i in range(n_lags, len(self.df)):
            # 输入特征：过去n_lags天的所有变量
            features = []
            for lag in range(n_lags):
                for feature in self.all_features:
                    features.append(self.df[feature].iloc[i - n_lags + lag])
            
            feature_data.append(features)
            # 目标：当前天的RUNOFF
            target_data.append(self.df[self.target_feature].iloc[i])
        
        # 创建特征名称
        feature_names = []
        for lag in range(n_lags):
            for feature in self.all_features:
                feature_names.append(f"{feature}_lag_{lag+1}")
        
        X = np.array(feature_data)
        y = np.array(target_data)
        
        print(f"✅ 特征矩阵形状: {X.shape}")
        print(f"✅ 目标向量形状: {y.shape}")
        print(f"✅ 特征总数: {len(feature_names)}")
        
        return X, y, feature_names
    
    def train_lightgbm_model(self, X, y, feature_names):
        """训练LightGBM模型进行特征重要性分析"""
        print("\n🚀 训练LightGBM模型")
        print("="*30)
        
        # 划分训练和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, shuffle=False
        )
        
        print(f"训练集: {X_train.shape}")
        print(f"测试集: {X_test.shape}")
        
        # 创建LightGBM数据集
        train_data = lgb.Dataset(X_train, label=y_train, feature_name=feature_names)
        valid_data = lgb.Dataset(X_test, label=y_test, reference=train_data, feature_name=feature_names)
        
        # LightGBM参数
        params = {
            'objective': 'regression',
            'metric': 'rmse',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': 0,
            'random_state': 42
        }
        
        # 训练模型
        print("🔄 开始训练...")
        self.lgb_model = lgb.train(
            params,
            train_data,
            valid_sets=[valid_data],
            num_boost_round=1000,
            callbacks=[lgb.early_stopping(50), lgb.log_evaluation(0)]
        )
        
        # 预测和评估
        y_pred = self.lgb_model.predict(X_test, num_iteration=self.lgb_model.best_iteration)
        
        mae = mean_absolute_error(y_test, y_pred)
        rmse = np.sqrt(mean_squared_error(y_test, y_pred))
        r2 = r2_score(y_test, y_pred)
        
        print(f"✅ 模型训练完成")
        print(f"📊 模型性能:")
        print(f"  MAE: {mae:.2f}")
        print(f"  RMSE: {rmse:.2f}")
        print(f"  R²: {r2:.4f}")
        
        return y_test, y_pred
    
    def analyze_feature_importance(self, feature_names, top_n=50):
        """分析特征重要性"""
        print(f"\n📊 分析特征重要性 (Top {top_n})")
        print("="*40)
        
        # 获取特征重要性
        importance = self.lgb_model.feature_importance(importance_type='gain')
        
        # 创建特征重要性DataFrame
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': importance
        }).sort_values('importance', ascending=False)
        
        self.feature_importance = importance_df
        
        print(f"Top {top_n} 重要特征:")
        print("-" * 60)
        for i, (_, row) in enumerate(importance_df.head(top_n).iterrows(), 1):
            feature_name = row['feature']
            importance_val = row['importance']
            
            # 解析特征名称
            if '_lag_' in feature_name:
                base_feature, lag_part = feature_name.rsplit('_lag_', 1)
                lag_num = lag_part
                print(f"{i:2d}. {base_feature:<8} (滞后{lag_num:>2}天): {importance_val:>8.0f}")
            else:
                print(f"{i:2d}. {feature_name:<15}: {importance_val:>8.0f}")
        
        return importance_df
    
    def select_top_features_by_variable(self, top_k=5):
        """按变量选择最重要的特征"""
        print(f"\n🎯 选择最重要的{top_k}个变量")
        print("="*40)
        
        # 按基础变量聚合重要性
        variable_importance = {}
        
        for _, row in self.feature_importance.iterrows():
            feature_name = row['feature']
            importance_val = row['importance']
            
            # 提取基础变量名
            if '_lag_' in feature_name:
                base_variable = feature_name.rsplit('_lag_', 1)[0]
            else:
                base_variable = feature_name
            
            if base_variable not in variable_importance:
                variable_importance[base_variable] = 0
            variable_importance[base_variable] += importance_val
        
        # 排序并选择top_k
        sorted_variables = sorted(variable_importance.items(), key=lambda x: x[1], reverse=True)
        
        print("变量重要性排序:")
        print("-" * 40)
        for i, (variable, total_importance) in enumerate(sorted_variables, 1):
            percentage = (total_importance / sum(variable_importance.values())) * 100
            status = "✅" if i <= top_k else "  "
            print(f"{status} {i:2d}. {variable:<8}: {total_importance:>8.0f} ({percentage:>5.1f}%)")
        
        # 选择top_k变量
        self.selected_features = [var for var, _ in sorted_variables[:top_k]]
        
        print(f"\n🎯 选定的{top_k}个最重要特征:")
        for i, feature in enumerate(self.selected_features, 1):
            print(f"  {i}. {feature}")
        
        return self.selected_features, variable_importance
    
    def save_results(self):
        """保存分析结果"""
        print("\n💾 保存分析结果")
        print("="*30)
        
        results = {
            "analysis_date": pd.Timestamp.now().isoformat(),
            "data_file": self.data_file,
            "total_features_analyzed": len(self.feature_importance),
            "selected_features": self.selected_features,
            "feature_importance_summary": {
                var: float(imp) for var, imp in 
                self.select_top_features_by_variable(len(self.all_features))[1].items()
            },
            "top_50_features": self.feature_importance.head(50).to_dict('records')
        }
        
        # 保存到JSON文件
        results_file = "lightgbm_feature_selection_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"✅ 结果已保存到: {results_file}")
        
        # 保存特征重要性CSV
        importance_file = "feature_importance_detailed.csv"
        self.feature_importance.to_csv(importance_file, index=False, encoding='utf-8')
        print(f"✅ 详细特征重要性已保存到: {importance_file}")
        
        return results
    
    def run_complete_analysis(self, n_lags=30, top_k=5):
        """运行完整的特征选择分析"""
        print("🎯 LightGBM特征重要性分析")
        print("="*50)
        
        # 1. 加载数据
        if not self.load_and_prepare_data():
            return None
        
        # 2. 创建时间序列特征
        X, y, feature_names = self.create_time_series_features(n_lags)
        
        # 3. 训练LightGBM模型
        y_test, y_pred = self.train_lightgbm_model(X, y, feature_names)
        
        # 4. 分析特征重要性
        importance_df = self.analyze_feature_importance(feature_names)
        
        # 5. 选择最重要的特征
        selected_features, variable_importance = self.select_top_features_by_variable(top_k)
        
        # 6. 保存结果
        results = self.save_results()
        
        print(f"\n✅ 特征选择分析完成!")
        print(f"🎯 推荐的{top_k}个输入特征: {', '.join(selected_features)}")
        
        return selected_features, results

def main():
    """主函数"""
    selector = LightGBMFeatureSelector()
    
    print("🎯 基于LightGBM的特征重要性分析")
    print("="*50)
    print("目标: 选择与RUNOFF最相关的5个特征作为TimeMixer输入")
    print()
    
    # 检查数据文件
    if not Path(selector.data_file).exists():
        print(f"❌ 数据文件不存在: {selector.data_file}")
        return
    
    # 运行分析
    try:
        selected_features, results = selector.run_complete_analysis(n_lags=30, top_k=5)
        
        if selected_features:
            print("\n🎉 分析成功完成!")
            print("="*30)
            print("📋 推荐配置:")
            print(f"  输入特征数: 5个")
            print(f"  选定特征: {', '.join(selected_features)}")
            print(f"  数据文件: {selector.data_file}")
            print()
            print("💡 下一步:")
            print("1. 使用这5个特征更新TimeMixer配置")
            print("2. 重新训练模型验证性能")
            print("3. 对比10特征vs5特征的效果")
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
