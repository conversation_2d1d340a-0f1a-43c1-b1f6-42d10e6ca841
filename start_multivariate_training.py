"""
多变量TimeMixer训练快速启动器
=============================

专门用于启动多变量气象-径流预测训练的便捷脚本。
"""

import os
import sys
from pathlib import Path

def check_data_file():
    """检查数据文件是否存在"""
    data_file = "1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv"
    if not Path(data_file).exists():
        print(f"❌ 数据文件不存在: {data_file}")
        print("请确保多变量气象数据文件在当前目录中")
        return False
    return True

def show_multivariate_info():
    """显示多变量配置信息"""
    print("🎯 TimeMixer多变量时间序列预测系统")
    print("="*60)
    print("📊 数据配置:")
    print("  输入特征: 10个变量 (包含RUNOFF历史值)")
    print("    - RUNOFF (径流历史值) ⭐ 新增")
    print("    - TEMP (温度)")
    print("    - DEWP (露点温度)")
    print("    - SLP (海平面气压)")
    print("    - VISIB (能见度)")
    print("    - WDSP (风速)")
    print("    - MXSPD (最大风速)")
    print("    - MAX (最高温度)")
    print("    - MIN (最低温度)")
    print("    - PRCP (降水量)")
    print("  输出目标: 1个径流变量 (RUNOFF未来值)")
    print("  数据范围: 1971-2017年 (47年, 17,167天)")
    print("  💡 关键改进: 包含RUNOFF历史值，利用时间序列自相关性")
    print()

def show_available_configs():
    """显示可用配置"""
    configs = [
        ("快速验证", "30天→7天", "2层, 160维", "50轮", "⚡ 推荐新手"),
        ("季节性预测", "60天→14天", "3层, 192维", "100轮", "🌊 平衡性能"),
        ("长期预测", "180天→30天", "4层, 256维", "150轮", "📅 高复杂度"),
        ("极端事件", "180天→14天", "4层, 384维", "100轮", "🎯 最高复杂"),
        ("学习率实验", "72天→18天", "2层, 96维", "30轮", "⚡ 快速实验"),
        ("架构实验", "96天→24天", "2层, 512维", "60轮", "🔬 宽网络"),
        ("全数据训练", "90天→15天", "4层, 384维", "200轮", "🌟 最大规模"),
        ("多变量特化", "45天→10天", "3层, 320维", "120轮", "🎯 含RUNOFF历史")
    ]
    
    print("📋 可用的训练配置:")
    print("-" * 80)
    for i, (name, seq, model, epochs, desc) in enumerate(configs, 1):
        print(f"{i:2d}. {name:<12} | {seq:<12} | {model:<12} | {epochs:<8} | {desc}")
    print()

def main():
    """主函数"""
    print("🚀 TimeMixer多变量训练快速启动器")
    print("="*50)
    
    # 检查数据文件
    if not check_data_file():
        return
    
    # 显示系统信息
    show_multivariate_info()
    
    # 显示可用配置
    show_available_configs()
    
    print("选择操作:")
    print("1. 🔍 验证数据质量")
    print("2. ⚡ 快速开始训练 (推荐配置)")
    print("3. 📋 选择特定配置训练")
    print("4. 🚀 批量运行所有配置")
    print("5. 🔧 高级训练界面")
    print("6. 📊 查看历史结果")
    print("7. 📖 查看详细文档")
    print("8. 退出")
    
    while True:
        choice = input("\n请选择 (1-8): ").strip()
        
        if choice == "1":
            print("\n🔍 启动数据验证器...")
            os.system("python multivariate_data_validator.py")
            break
            
        elif choice == "2":
            print("\n⚡ 启动快速训练 (配置1: 快速验证_含RUNOFF历史)...")
            print("这将使用以下配置:")
            print("  - 输入特征: 10个 (包含RUNOFF历史值)")
            print("  - 输入序列: 30天")
            print("  - 预测序列: 7天")
            print("  - 模型: 2层, 160维度")
            print("  - 训练: 50轮")
            print("  - 样本: 1000个 (快速验证)")
            print("  💡 关键改进: 利用径流历史值的自相关性")
            
            confirm = input("\n确认开始训练? (y/n): ").lower()
            if confirm == 'y':
                # 创建临时脚本来运行特定配置
                script_content = '''
import sys
sys.path.append('.')
from my_parameters import get_my_parameters
from compatible_training_runner import run_compatible_training

configs = get_my_parameters()
quick_config = configs[0]  # 第一个配置
print(f"\\n🚀 开始快速训练: {quick_config['name']}")
result = run_compatible_training(quick_config)
if result:
    print(f"\\n✅ 训练完成! 训练ID: {result['training_id']}")
    print(f"MAE: {result['mae']:.2f}, R²: {result['r2']:.4f}")
else:
    print("\\n❌ 训练失败")
'''
                with open('temp_quick_train.py', 'w', encoding='utf-8') as f:
                    f.write(script_content)
                os.system("python temp_quick_train.py")
                os.remove('temp_quick_train.py')
            break
            
        elif choice == "3":
            print("\n📋 启动配置选择训练...")
            os.system("python run_my_training.py")
            break
            
        elif choice == "4":
            print("\n🚀 启动批量训练...")
            print("⚠️ 这将依次运行所有8个配置，可能需要较长时间")
            confirm = input("确认开始批量训练? (y/n): ").lower()
            if confirm == 'y':
                # 创建临时脚本来运行批量训练
                script_content = '''
import sys
sys.path.append('.')
from my_parameters import get_my_parameters
from compatible_training_runner import run_compatible_training
import time

configs = get_my_parameters()
print(f"\\n🚀 开始批量训练 (共{len(configs)}个配置)")

results = []
for i, config in enumerate(configs, 1):
    print(f"\\n{'='*60}")
    print(f"进度: {i}/{len(configs)} - {config['name']}")
    print(f"{'='*60}")
    
    try:
        result = run_compatible_training(config)
        if result:
            results.append({
                'name': config['name'],
                'training_id': result['training_id'],
                'mae': result['mae'],
                'r2': result['r2']
            })
            print(f"✅ {config['name']} 完成")
        else:
            print(f"❌ {config['name']} 失败")
    except Exception as e:
        print(f"❌ {config['name']} 异常: {e}")
    
    if i < len(configs):
        print("等待3秒后继续...")
        time.sleep(3)

print(f"\\n📊 批量训练完成! 成功: {len(results)}/{len(configs)}")
for result in results:
    print(f"  {result['name']}: MAE={result['mae']:.2f}, R²={result['r2']:.4f}")
'''
                with open('temp_batch_train.py', 'w', encoding='utf-8') as f:
                    f.write(script_content)
                os.system("python temp_batch_train.py")
                os.remove('temp_batch_train.py')
            break
            
        elif choice == "5":
            print("\n🔧 启动高级训练界面...")
            os.system("python run_advanced_training.py")
            break
            
        elif choice == "6":
            print("\n📊 查看历史训练结果...")
            results_file = "timemixer_evaluation_results.csv"
            if Path(results_file).exists():
                print(f"结果文件: {results_file}")
                try:
                    import pandas as pd
                    df = pd.read_csv(results_file)
                    print(f"\n最近5次训练结果:")
                    print(df[['Training_ID', 'MAE', 'RMSE', 'NSE', 'R2']].tail().to_string(index=False))
                except:
                    print("请手动查看文件或安装pandas")
            else:
                print("暂无训练结果")
            break
            
        elif choice == "7":
            print("\n📖 可用文档:")
            docs = [
                ("MULTIVARIATE_CONFIGURATION_SUMMARY.md", "多变量配置总结"),
                ("VERSION_CONTROL_GUIDE.md", "版本控制指南"),
                ("PROJECT_GUIDE.md", "项目使用指南"),
                ("DATA_PREPROCESSING_GUIDE.md", "数据预处理指南")
            ]
            
            for doc, desc in docs:
                status = "✅" if Path(doc).exists() else "❌"
                print(f"  {status} {doc} - {desc}")
            
            print(f"\n💡 建议先阅读: MULTIVARIATE_CONFIGURATION_SUMMARY.md")
            break
            
        elif choice == "8":
            print("👋 再见!")
            break
            
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    main()
