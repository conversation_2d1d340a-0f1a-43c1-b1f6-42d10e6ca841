import numpy as np
import pandas as pd
from pypots.optim import <PERSON>
from pypots.forecasting import TimeMixer
from pypots.nn.functional import calc_mae
import warnings

warnings.filterwarnings('ignore')

# --- 您的数据集路径和列名 ---
your_data_path = '1964-2017dailyRunoff.csv'
date_column_name = 'DATA'
target_column_name = 'runoff'

# --- 定义模型所需的输入和预测步长 ---
N_STEPS_PER_SAMPLE = 96
N_PRED_STEPS = 24

# --- 您的数据集加载和预处理逻辑 ---
try:
    # 尝试不同的编码
    # 选项 1: 尝试 'latin1' 或 'cp1252' (Windows常用)
    # df = pd.read_csv(your_data_path, parse_dates=[date_column_name], index_col=date_column_name, date_format='%Y/%m/%d', encoding='latin1')
    # df = pd.read_csv(your_data_path, parse_dates=[date_column_name], index_col=date_column_name, date_format='%Y/%m/%d', encoding='cp1252')

    # 选项 2: 尝试 'gbk' 或 'gb2312' (如果您的系统或文件来源是中文环境)
    # df = pd.read_csv(your_data_path, parse_dates=[date_column_name], index_col=date_column_name, date_format='%Y/%m/%d', encoding='gbk')

    # 选项 3: 尝试 'shift_jis' 或 'cp932' (如果您的系统或文件来源是日文环境)
    # df = pd.read_csv(your_data_path, parse_dates=[date_column_name], index_col=date_column_name, date_format='%Y/%m/%d', encoding='shift_jis')
    # df = pd.read_csv(your_data_path, parse_dates=[date_column_name], index_col=date_column_name, date_format='%Y/%m/%d', encoding='cp932')

    # 读取CSV文件，DATA列格式为'1950/1/1'
    df = pd.read_csv(your_data_path)
    # 手动转换日期列
    df[date_column_name] = pd.to_datetime(df[date_column_name], format='%Y/%m/%d')
    # 设置日期为索引
    df = df.set_index(date_column_name)
    
    # ... (其余代码保持不变，包括缺失值处理、数据整形、模型训练等) ...

    # 2. 检查数据并处理缺失值 (如果存在)
    full_time_series_raw = df[target_column_name].values.astype(np.float32)

    print(f"原始数据总长度: {len(full_time_series_raw)}")
    print(f"原始数据缺失值数量: {np.isnan(full_time_series_raw).sum()}")

    # 简单处理缺失值：这里我们使用均值填充。
    if np.isnan(full_time_series_raw).any():
        print("警告：数据中存在缺失值，将使用均值填充。建议在生产环境中使用更复杂的插补策略。")
        mean_value = np.nanmean(full_time_series_raw)
        full_time_series = np.nan_to_num(full_time_series_raw, nan=mean_value)
    else:
        full_time_series = full_time_series_raw

    # 3. 将一维时间序列转换为 TimeMixer++ 所需的三维格式
    # (n_samples, n_steps, n_features)
    # 对于单变量数据，n_features = 1
    total_sample_len = N_STEPS_PER_SAMPLE + N_PRED_STEPS
    n_samples = (len(full_time_series) - total_sample_len) + 1

    if n_samples <= 0:
        raise ValueError(
            f"数据集太短，无法创建足够长度为 {total_sample_len} 的样本。"
            f"当前数据长度为 {len(full_time_series)}。请检查数据长度和 N_STEPS_PER_SAMPLE / N_PRED_STEPS 设置。"
        )

    your_dataset_X = np.zeros((n_samples, total_sample_len, 1), dtype=np.float32)

    for i in range(n_samples):
        your_dataset_X[i, :, 0] = full_time_series[i : i + total_sample_len]

    print(f"处理后用于模型的数据形状 (n_samples, total_sample_len, n_features): {your_dataset_X.shape}")

except FileNotFoundError:
    print(f"错误：未找到数据集文件在 {your_data_path}。请检查路径。")
    exit()
except Exception as e:
    print(f"加载或处理数据集时发生错误：{e}") # 这里会捕获到您当前的错误
    exit()

# --- 您的数据集现在在 your_dataset_X 变量中，形状为 (n_samples, total_time_len, 1) ---

# 定义一些从您的数据中获取的参数
N_SAMPLES = your_dataset_X.shape[0]
ACTUAL_N_FEATURES = your_dataset_X.shape[2] # 这将是 1，因为是单变量

# --- 划分数据集 ---
def split_data_for_forecasting(data_X: np.ndarray, n_steps: int, n_pred_steps: int,
                               train_ratio: float = 0.7, val_ratio: float = 0.15) -> tuple:
    """
    为PyPOTS forecasting准备数据集，需要分离输入和预测目标
    """
    n_total_samples = data_X.shape[0]
    train_end = int(n_total_samples * train_ratio)
    val_end = int(n_total_samples * (train_ratio + val_ratio))

    # 分离输入部分和预测目标部分
    X_input = data_X[:, :n_steps, :]  # 前n_steps作为输入
    X_pred = data_X[:, n_steps:, :]   # 后n_pred_steps作为预测目标

    train_data = {
        'X': X_input[:train_end],
        'X_pred': X_pred[:train_end]
    }
    val_data = {
        'X': X_input[train_end:val_end],
        'X_pred': X_pred[train_end:val_end]
    }
    test_data = {
        'X': X_input[val_end:],
        'X_pred': X_pred[val_end:]
    }
    return train_data, val_data, test_data

dataset_for_FORE_training, dataset_for_FORE_validating, dataset_for_FORE_testing = \
    split_data_for_forecasting(your_dataset_X, N_STEPS_PER_SAMPLE, N_PRED_STEPS)

print(f"\n数据集划分结果:")
print(f"  训练集形状: {dataset_for_FORE_training['X'].shape}")
print(f"  验证集形状: {dataset_for_FORE_validating['X'].shape}")
print(f"  测试集形状: {dataset_for_FORE_testing['X'].shape}")

# --- 更新 TimeMixer 模型参数 ---
timemixer = TimeMixer(
    n_steps = N_STEPS_PER_SAMPLE,
    n_features = ACTUAL_N_FEATURES,
    n_pred_steps = N_PRED_STEPS,
    n_pred_features = ACTUAL_N_FEATURES,
    term = "short",
    n_layers=2,
    top_k=5,
    d_model=32,
    d_ffn=32,
    moving_avg=25,
    downsampling_window=2,
    downsampling_layers=1,
    use_norm=True,
    dropout=0.1,
    epochs=10,
    patience=3,
    optimizer=Adam(lr=1e-3),
    num_workers=0,
    device=None,
    saving_path="your_runoff_results/forecasting/timemixer",
    model_saving_strategy="best",
)

print(f"\nTimeMixer++ 模型初始化完成，参数如下:")
print(f"  输入步长 (n_steps): {timemixer.n_steps}")
print(f"  特征数量 (n_features): {timemixer.n_features}")
print(f"  预测步长 (n_pred_steps): {timemixer.n_pred_steps}")
print(f"  训练轮次 (epochs): {timemixer.epochs}")
print(f"  模型将保存到: {timemixer.saving_path}")

# 训练模型
print("\n开始训练 TimeMixer++ 模型...")
timemixer.fit(train_set=dataset_for_FORE_training, val_set=dataset_for_FORE_validating)
print("模型训练完成。")

# 进行预测
print("\n开始在测试集上进行预测...")
timemixer_results = timemixer.predict(dataset_for_FORE_testing)
timemixer_prediction = timemixer_results["forecasting"]
print(f"预测结果形状: {timemixer_prediction.shape}")

# 计算误差
true_values_for_testing = dataset_for_FORE_testing['X_pred']  # 使用预测目标作为真实值
testing_mae = calc_mae(
    timemixer_prediction,
    true_values_for_testing,
    np.ones_like(true_values_for_testing, dtype=int)
)
print(f"\n测试集平均绝对误差 (MAE): {testing_mae:.4f}")

print("\n--- 预测流程完成 ---")