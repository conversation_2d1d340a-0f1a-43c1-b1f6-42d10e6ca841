"""
GPU检测脚本
===========
"""

import torch

def check_gpu():
    print("GPU环境检测")
    print("="*40)
    
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            
        print(f"当前设备: {torch.cuda.current_device()}")
        
        # 测试GPU内存
        device = torch.device('cuda:0')
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        
        # 简单测试
        try:
            x = torch.randn(100, 100).to(device)
            y = torch.randn(100, 100).to(device)
            z = torch.mm(x, y)
            print("✓ GPU计算测试成功")
        except Exception as e:
            print(f"✗ GPU计算测试失败: {e}")
    else:
        print("未检测到CUDA GPU")
        print("建议:")
        print("1. 检查GPU驱动是否安装")
        print("2. 安装CUDA版本的PyTorch")
        print("3. 重启系统")

if __name__ == "__main__":
    check_gpu()
