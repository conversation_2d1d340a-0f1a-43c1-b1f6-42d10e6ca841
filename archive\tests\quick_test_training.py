"""
快速测试训练 - 兼容版本
======================

运行一个快速的训练测试，验证系统是否正常工作。
兼容旧版本PyPOTS环境。
"""

def check_environment():
    """检查环境兼容性"""
    import sys
    import os

    # 添加主目录到路径
    main_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if main_dir not in sys.path:
        sys.path.insert(0, main_dir)

    try:
        from compatible_training_runner import run_compatible_training
        print("✓ 检测到兼容训练模块")
        return 'compatible'
    except ImportError:
        pass

    try:
        from single_training_runner import run_timemixer_training
        print("✓ 检测到标准训练模块")
        return 'standard'
    except ImportError:
        print("✗ 无法导入训练模块")
        return None

def run_quick_test_compatible():
    """运行兼容版本的快速测试"""
    from compatible_training_runner import run_compatible_training

    # 快速测试参数
    test_parameters = {
        'n_steps': 24,
        'n_pred_steps': 6,
        'n_layers': 1,
        'd_model': 32,
        'd_ffn': 64,
        'epochs': 3,
        'learning_rate': 1e-3,
        'dropout': 0.1,
        'device': 'cpu'
    }

    print("快速测试参数:")
    for key, value in test_parameters.items():
        print(f"  {key}: {value}")

    print("\n开始快速训练测试...")

    # 运行训练
    result = run_compatible_training(test_parameters)

    if result:
        print(f"\n✓ 快速测试成功完成！")
        print(f"训练ID: {result['training_id']}")
        print(f"结果:")
        print(f"  MAE: {result['mae']:.6f}")
        print(f"  RMSE: {result['rmse']:.6f}")
        print(f"  NSE: {result['nse']:.6f}")
        print(f"  R²: {result['r2']:.6f}")

        print(f"\n系统工作正常！您现在可以:")
        print(f"1. 运行 python compatible_training_runner.py 进行更多训练")
        print(f"2. 选择升级PyPOTS获得完整功能")
        print(f"3. 修改参数配置进行自定义训练")

        return True
    else:
        print(f"\n✗ 快速测试失败")
        return False

def run_quick_test_standard():
    """运行标准版本的快速测试"""
    from single_training_runner import run_timemixer_training

    # 定义一个快速测试的参数配置
    test_parameters = {
        'n_steps': 24,        # 较短的输入序列
        'n_pred_steps': 6,    # 较短的预测序列
        'n_layers': 1,        # 单层
        'd_model': 32,        # 小模型
        'd_ffn': 64,          # 小FFN
        'epochs': 3,          # 很少的训练轮次
        'learning_rate': 1e-3,
        'dropout': 0.1,
        'patience': 2
    }

    print("快速测试参数:")
    for key, value in test_parameters.items():
        print(f"  {key}: {value}")

    print("\n开始快速训练测试...")

    # 运行训练
    result = run_timemixer_training(test_parameters)

    if result:
        print(f"\n✓ 快速测试成功完成！")
        print(f"训练ID: {result['training_id']}")
        print(f"结果:")
        print(f"  MAE: {result['mae']:.6f}")
        print(f"  RMSE: {result['rmse']:.6f}")
        print(f"  NSE: {result['nse']:.6f}")
        print(f"  R²: {result['r2']:.6f}")

        print(f"\n系统工作正常！您现在可以:")
        print(f"1. 运行 python single_training_runner.py 进行单次训练")
        print(f"2. 运行 python batch_training_runner.py 进行批量训练")
        print(f"3. 修改参数配置进行自定义训练")

        return True
    else:
        print(f"\n✗ 快速测试失败")
        return False

def main():
    """主函数"""
    print("TimeMixer++ 快速测试训练")
    print("="*50)

    # 检查环境
    env_type = check_environment()

    if env_type == 'compatible':
        print("使用兼容模式进行快速测试...")
        success = run_quick_test_compatible()
    elif env_type == 'standard':
        print("使用标准模式进行快速测试...")
        success = run_quick_test_standard()
    else:
        print("环境检查失败，请确保已安装必要的依赖")
        print("\n建议:")
        print("1. 运行 python compatible_training_runner.py")
        print("2. 或升级PyPOTS: pip install pypots --upgrade")
        return

    if success:
        print(f"\n🎉 快速测试完成！系统运行正常。")
    else:
        print(f"\n❌ 快速测试失败，请检查环境配置。")

if __name__ == "__main__":
    main()
