"""
Fix Current Environment Packaging Conflicts
==========================================

This script fixes the packaging version conflicts in your current Anaconda environment
by forcefully reinstalling problematic packages.
"""

import subprocess
import sys
import os
import importlib

def run_command_force(command, description, ignore_errors=True):
    """Run a command with force and handle errors gracefully."""
    print(f"🔧 {description}...")
    print(f"   Command: {command}")
    
    try:
        # Use shell=True and capture output
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        if result.returncode == 0:
            print(f"   ✅ Success")
            return True
        else:
            print(f"   ⚠️  Warning (return code {result.returncode})")
            if not ignore_errors:
                print(f"   Error output: {result.stderr[:200]}...")
            return ignore_errors
            
    except subprocess.TimeoutExpired:
        print(f"   ⏰ Timeout - continuing anyway")
        return ignore_errors
    except Exception as e:
        print(f"   ❌ Exception: {str(e)[:100]}...")
        return ignore_errors

def force_reinstall_packaging():
    """Force reinstall packaging and related packages."""
    print("\n🔨 FORCE REINSTALLING PACKAGING COMPONENTS")
    print("=" * 60)
    
    # Step 1: Remove problematic packages
    print("\n1. Removing problematic packages...")
    remove_commands = [
        "pip uninstall packaging -y",
        "pip uninstall setuptools -y", 
        "pip uninstall wheel -y",
        "conda remove packaging --force -y",
        "conda remove setuptools --force -y",
    ]
    
    for cmd in remove_commands:
        run_command_force(cmd, f"Removing with: {cmd}")
    
    # Step 2: Clean pip cache
    print("\n2. Cleaning caches...")
    clean_commands = [
        "pip cache purge",
        "conda clean --all -y",
    ]
    
    for cmd in clean_commands:
        run_command_force(cmd, f"Cleaning: {cmd}")
    
    # Step 3: Reinstall core packages
    print("\n3. Reinstalling core packages...")
    install_commands = [
        "pip install --upgrade --force-reinstall pip",
        "pip install --upgrade --force-reinstall setuptools",
        "pip install --upgrade --force-reinstall wheel", 
        "pip install --upgrade --force-reinstall packaging>=20.0",
    ]
    
    for cmd in install_commands:
        run_command_force(cmd, f"Installing: {cmd}", ignore_errors=False)

def fix_transformers_conflict():
    """Fix transformers packaging conflict."""
    print("\n🤖 FIXING TRANSFORMERS CONFLICT")
    print("=" * 60)
    
    # Remove and reinstall transformers
    commands = [
        "pip uninstall transformers -y",
        "pip uninstall tokenizers -y", 
        "pip install packaging>=20.0",  # Ensure packaging is correct first
        "pip install transformers",
    ]
    
    for cmd in commands:
        run_command_force(cmd, f"Transformers fix: {cmd}")

def reinstall_pypots():
    """Reinstall PyPOTS cleanly."""
    print("\n📦 REINSTALLING PYPOTS")
    print("=" * 60)
    
    commands = [
        "pip uninstall pypots -y",
        "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu",
        "pip install pypots --no-cache-dir",
    ]
    
    for cmd in commands:
        run_command_force(cmd, f"PyPOTS: {cmd}")

def test_installation():
    """Test if the installation works."""
    print("\n🧪 TESTING INSTALLATION")
    print("=" * 60)
    
    tests = [
        ("packaging", "Testing packaging import"),
        ("transformers", "Testing transformers import"),
        ("pypots", "Testing PyPOTS import"),
    ]
    
    results = {}
    
    for package, description in tests:
        print(f"\n{description}...")
        try:
            # Force reload modules
            if package in sys.modules:
                del sys.modules[package]
            
            module = importlib.import_module(package)
            version = getattr(module, '__version__', 'Unknown')
            print(f"   ✅ {package} v{version}")
            results[package] = True
            
        except Exception as e:
            print(f"   ❌ {package} failed: {str(e)[:100]}...")
            results[package] = False
    
    # Test TimeMixer specifically
    print(f"\nTesting TimeMixer import...")
    try:
        from pypots.forecasting.timemixer import TimeMixer
        print(f"   ✅ TimeMixer imported successfully")
        results['timemixer'] = True
    except Exception as e:
        print(f"   ❌ TimeMixer failed: {str(e)[:100]}...")
        results['timemixer'] = False
    
    return results

def nuclear_option():
    """Nuclear option - force reinstall everything."""
    print("\n💥 NUCLEAR OPTION - FORCE REINSTALL EVERYTHING")
    print("=" * 60)
    print("This will take several minutes...")
    
    # Get list of all installed packages
    try:
        result = subprocess.run("pip list --format=freeze", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            packages = [line.split('==')[0] for line in result.stdout.split('\n') if '==' in line]
            print(f"Found {len(packages)} packages to potentially reinstall")
        else:
            packages = []
    except:
        packages = []
    
    # Core packages that need to be fixed
    critical_packages = [
        'packaging', 'setuptools', 'wheel', 'pip',
        'transformers', 'tokenizers', 'torch', 'pypots'
    ]
    
    print("\nReinstalling critical packages...")
    for package in critical_packages:
        cmd = f"pip install --upgrade --force-reinstall {package} --no-cache-dir"
        run_command_force(cmd, f"Nuclear reinstall: {package}")

def main():
    """Main function to fix the environment."""
    print("🔧 FIXING CURRENT ENVIRONMENT PACKAGING CONFLICTS")
    print("=" * 70)
    print("This script will fix packaging conflicts in your current environment")
    print("without creating a new conda environment.")
    print()
    
    # Ask user for confirmation
    print("⚠️  WARNING: This will modify your current environment!")
    print("It may take 10-15 minutes to complete.")
    response = input("Do you want to proceed? (y/N): ").strip().lower()
    
    if response not in ['y', 'yes']:
        print("Operation cancelled.")
        return
    
    print("\n🚀 Starting environment fix...")
    
    # Method 1: Try gentle fix first
    print("\n" + "="*70)
    print("METHOD 1: GENTLE FIX")
    print("="*70)
    
    force_reinstall_packaging()
    fix_transformers_conflict()
    reinstall_pypots()
    
    # Test if it worked
    results = test_installation()
    
    if all(results.values()):
        print("\n🎉 SUCCESS! Environment fixed with gentle method.")
        print("You can now run: python direct_timemixer_run.py")
        return
    
    # Method 2: Nuclear option if gentle fix failed
    print("\n" + "="*70)
    print("METHOD 2: NUCLEAR OPTION")
    print("="*70)
    print("Gentle fix didn't work completely. Trying nuclear option...")
    
    nuclear_response = input("Proceed with nuclear option? (y/N): ").strip().lower()
    if nuclear_response in ['y', 'yes']:
        nuclear_option()
        
        # Final test
        print("\n🧪 FINAL TEST AFTER NUCLEAR OPTION")
        final_results = test_installation()
        
        if all(final_results.values()):
            print("\n🎉 SUCCESS! Environment fixed with nuclear option.")
        else:
            print("\n😞 Some issues remain. You may need to:")
            print("1. Restart your Python environment/IDE")
            print("2. Or consider creating a new conda environment")
    
    print("\n" + "="*70)
    print("ENVIRONMENT FIX COMPLETED")
    print("="*70)
    print("Next steps:")
    print("1. Restart your IDE/terminal")
    print("2. Run: python direct_timemixer_run.py")
    print("3. If issues persist, consider creating new environment")

if __name__ == "__main__":
    main()
