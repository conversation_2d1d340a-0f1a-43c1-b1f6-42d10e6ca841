@echo off
echo ========================================
echo Creating Clean PyPOTS Environment
echo ========================================
echo.

echo Step 1: Creating new conda environment...
conda create -n pypots python=3.9 -y
echo.

echo Step 2: Activating environment...
call conda activate pypots
echo.

echo Step 3: Installing basic packages...
conda install pandas numpy matplotlib -y
echo.

echo Step 4: Installing PyTorch...
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
echo.

echo Step 5: Installing PyPOTS...
pip install pypots
echo.

echo Step 6: Testing installation...
python -c "import pypots; print('PyPOTS version:', pypots.__version__); from pypots.forecasting.timemixer import TimeMixer; print('TimeMixer imported successfully!')"
echo.

echo ========================================
echo Environment Setup Complete!
echo ========================================
echo.
echo To use this environment:
echo 1. conda activate pypots
echo 2. python direct_timemixer_run.py
echo.
pause
