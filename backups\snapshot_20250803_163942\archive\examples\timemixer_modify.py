import numpy as np
import pandas as pd
from pypots.optim import <PERSON>
from pypots.forecasting import TimeMixer
from pypots.nn.functional import calc_mae
import warnings
from sklearn.metrics import r2_score
import matplotlib.pyplot as plt

warnings.filterwarnings('ignore')

def calculate_nse(y_true, y_pred):
    """
    计算Nash-Sutcliffe效率系数 (NSE)
    NSE = 1 - (SS_res / SS_tot)
    其中 SS_res = Σ(y_true - y_pred)²
         SS_tot = Σ(y_true - mean(y_true))²
    
    NSE范围: (-∞, 1]
    NSE = 1: 完美预测
    NSE = 0: 预测效果等同于使用观测值的平均值
    NSE < 0: 预测效果比使用平均值还差
    """
    y_true_flat = y_true.flatten()
    y_pred_flat = y_pred.flatten()
    
    # 移除NaN值
    mask = ~(np.isnan(y_true_flat) | np.isnan(y_pred_flat))
    y_true_clean = y_true_flat[mask]
    y_pred_clean = y_pred_flat[mask]
    
    if len(y_true_clean) == 0:
        return np.nan
    
    # 计算NSE
    ss_res = np.sum((y_true_clean - y_pred_clean) ** 2)
    ss_tot = np.sum((y_true_clean - np.mean(y_true_clean)) ** 2)
    
    if ss_tot == 0:
        return np.nan
    
    nse = 1 - (ss_res / ss_tot)
    return nse

def calculate_r2(y_true, y_pred):
    """
    计算决定系数 (R²)
    使用sklearn的r2_score函数
    """
    y_true_flat = y_true.flatten()
    y_pred_flat = y_pred.flatten()
    
    # 移除NaN值
    mask = ~(np.isnan(y_true_flat) | np.isnan(y_pred_flat))
    y_true_clean = y_true_flat[mask]
    y_pred_clean = y_pred_flat[mask]
    
    if len(y_true_clean) == 0:
        return np.nan
    
    return r2_score(y_true_clean, y_pred_clean)

def calculate_rmse(y_true, y_pred):
    """
    计算均方根误差 (RMSE)
    """
    y_true_flat = y_true.flatten()
    y_pred_flat = y_pred.flatten()
    
    # 移除NaN值
    mask = ~(np.isnan(y_true_flat) | np.isnan(y_pred_flat))
    y_true_clean = y_true_flat[mask]
    y_pred_clean = y_pred_flat[mask]
    
    if len(y_true_clean) == 0:
        return np.nan
    
    mse = np.mean((y_true_clean - y_pred_clean) ** 2)
    return np.sqrt(mse)

def plot_prediction_results(y_true, y_pred, save_path="prediction_results.png"):
    """
    绘制预测结果对比图
    """
    # 选择前几个样本进行可视化
    n_samples_to_plot = min(4, y_true.shape[0])
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    axes = axes.flatten()
    
    for i in range(n_samples_to_plot):
        ax = axes[i]
        
        days = range(1, y_true.shape[1] + 1)
        ax.plot(days, y_true[i, :, 0], 'b-', linewidth=2, label='真实值', marker='o')
        ax.plot(days, y_pred[i, :, 0], 'r--', linewidth=2, label='预测值', marker='s')
        
        ax.set_title(f'样本 {i+1}: 24天径流预测对比')
        ax.set_xlabel('预测天数')
        ax.set_ylabel('径流量')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"预测结果图表已保存到: {save_path}")
    plt.show()

# --- 您的数据集路径和列名 ---
your_data_path = 'C:/Users/<USER>/Desktop/timemix/1964-2017dailyRunoff.csv'
date_column_name = 'DATA'
target_column_name = 'runoff'

# --- 定义模型所需的输入和预测步长 ---
N_STEPS_PER_SAMPLE = 192
N_PRED_STEPS = 3

# --- 您的数据集加载和预处理逻辑 ---
try:
    # 读取CSV文件，DATA列格式为'1950/1/1'
    df = pd.read_csv(your_data_path)
    # 手动转换日期列
    df[date_column_name] = pd.to_datetime(df[date_column_name], format='%Y/%m/%d')
    # 设置日期为索引
    df = df.set_index(date_column_name)
    
    # ... (其余代码保持不变，包括缺失值处理、数据整形、模型训练等) ...

    # 2. 检查数据并处理缺失值 (如果存在)
    full_time_series_raw = df[target_column_name].values.astype(np.float32)

    print(f"原始数据总长度: {len(full_time_series_raw)}")
    print(f"原始数据缺失值数量: {np.isnan(full_time_series_raw).sum()}")

    # 简单处理缺失值：这里我们使用均值填充。
    if np.isnan(full_time_series_raw).any():
        print("警告：数据中存在缺失值，将使用均值填充。建议在生产环境中使用更复杂的插补策略。")
        mean_value = np.nanmean(full_time_series_raw)
        full_time_series = np.nan_to_num(full_time_series_raw, nan=mean_value)
    else:
        full_time_series = full_time_series_raw

    # 3. 将一维时间序列转换为 TimeMixer++ 所需的三维格式
    # (n_samples, n_steps, n_features)
    # 对于单变量数据，n_features = 1
    total_sample_len = N_STEPS_PER_SAMPLE + N_PRED_STEPS
    n_samples = (len(full_time_series) - total_sample_len) + 1

    if n_samples <= 0:
        raise ValueError(
            f"数据集太短，无法创建足够长度为 {total_sample_len} 的样本。"
            f"当前数据长度为 {len(full_time_series)}。请检查数据长度和 N_STEPS_PER_SAMPLE / N_PRED_STEPS 设置。"
        )

    your_dataset_X = np.zeros((n_samples, total_sample_len, 1), dtype=np.float32)

    for i in range(n_samples):
        your_dataset_X[i, :, 0] = full_time_series[i : i + total_sample_len]

    print(f"处理后用于模型的数据形状 (n_samples, total_sample_len, n_features): {your_dataset_X.shape}")

except FileNotFoundError:
    print(f"错误：未找到数据集文件在 {your_data_path}。请检查路径。")
    exit()
except Exception as e:
    print(f"加载或处理数据集时发生错误：{e}") # 这里会捕获到您当前的错误
    exit()

# --- 您的数据集现在在 your_dataset_X 变量中，形状为 (n_samples, total_time_len, 1) ---

# 定义一些从您的数据中获取的参数
N_SAMPLES = your_dataset_X.shape[0]
ACTUAL_N_FEATURES = your_dataset_X.shape[2] # 这将是 1，因为是单变量

# --- 划分数据集 ---
def split_data_for_forecasting(data_X: np.ndarray, n_steps: int, n_pred_steps: int,
                               train_ratio: float = 0.7, val_ratio: float = 0.15) -> tuple:
    """
    为PyPOTS forecasting准备数据集，需要分离输入和预测目标
    """
    n_total_samples = data_X.shape[0]
    train_end = int(n_total_samples * train_ratio)
    val_end = int(n_total_samples * (train_ratio + val_ratio))
    
    # 分离输入部分和预测目标部分
    X_input = data_X[:, :n_steps, :]  # 前n_steps作为输入
    X_pred = data_X[:, n_steps:, :]   # 后n_pred_steps作为预测目标
    
    train_data = {
        'X': X_input[:train_end],
        'X_pred': X_pred[:train_end]
    }
    val_data = {
        'X': X_input[train_end:val_end],
        'X_pred': X_pred[train_end:val_end]
    }
    test_data = {
        'X': X_input[val_end:],
        'X_pred': X_pred[val_end:]
    }
    return train_data, val_data, test_data

dataset_for_FORE_training, dataset_for_FORE_validating, dataset_for_FORE_testing = \
    split_data_for_forecasting(your_dataset_X, N_STEPS_PER_SAMPLE, N_PRED_STEPS)

print(f"\n数据集划分结果:")
print(f"  训练集形状: {dataset_for_FORE_training['X'].shape}")
print(f"  验证集形状: {dataset_for_FORE_validating['X'].shape}")
print(f"  测试集形状: {dataset_for_FORE_testing['X'].shape}")

# --- 更新 TimeMixer 模型参数 ---
timemixer = TimeMixer(
    n_steps = N_STEPS_PER_SAMPLE,
    n_features = ACTUAL_N_FEATURES,
    n_pred_steps = N_PRED_STEPS,
    n_pred_features = ACTUAL_N_FEATURES,
    term = "short",
    n_layers=3,
    top_k=8,
    d_model=128,
    d_ffn=128,
    moving_avg=25,
    downsampling_window=3,
    downsampling_layers=2,
    use_norm=True,
    dropout=0.1,
    epochs=200,
    patience=30,
    optimizer=Adam(lr=1e-3),
    num_workers=0,
    device=None,
    saving_path="your_runoff_results/forecasting/timemixer_modify_v2",
    model_saving_strategy="best",
)

print(f"\nTimeMixer++ 模型初始化完成，参数如下:")
print(f"  输入步长 (n_steps): {timemixer.n_steps}")
print(f"  特征数量 (n_features): {timemixer.n_features}")
print(f"  预测步长 (n_pred_steps): {timemixer.n_pred_steps}")
print(f"  训练轮次 (epochs): {timemixer.epochs}")
print(f"  模型将保存到: {timemixer.saving_path}")

# 训练模型
print("\n开始训练 TimeMixer++ 模型...")
timemixer.fit(train_set=dataset_for_FORE_training, val_set=dataset_for_FORE_validating)
print("模型训练完成。")

# 进行预测
print("\n开始在测试集上进行预测...")
timemixer_results = timemixer.predict(dataset_for_FORE_testing)
timemixer_prediction = timemixer_results["forecasting"]
print(f"预测结果形状: {timemixer_prediction.shape}")

# 计算多种评估指标
true_values_for_testing = dataset_for_FORE_testing['X_pred']  # 使用预测目标作为真实值

print(f"\n=== 模型评估结果 ===")
print(f"测试样本数量: {timemixer_prediction.shape[0]}")
print(f"预测时间步长: {timemixer_prediction.shape[1]}")

# MAE (平均绝对误差)
testing_mae = calc_mae(
    timemixer_prediction,
    true_values_for_testing,
    np.ones_like(true_values_for_testing, dtype=int)
)
print(f"平均绝对误差 (MAE): {testing_mae:.4f}")

# RMSE (均方根误差)
testing_rmse = calculate_rmse(true_values_for_testing, timemixer_prediction)
print(f"均方根误差 (RMSE): {testing_rmse:.4f}")

# NSE (Nash-Sutcliffe效率系数)
testing_nse = calculate_nse(true_values_for_testing, timemixer_prediction)
print(f"Nash-Sutcliffe效率系数 (NSE): {testing_nse:.4f}")

# R² (决定系数)
testing_r2 = calculate_r2(true_values_for_testing, timemixer_prediction)
print(f"决定系数 (R²): {testing_r2:.4f}")

# 评估指标解释
print(f"\n=== 评估指标解释 ===")
print(f"MAE: 平均绝对误差，越小越好")
print(f"RMSE: 均方根误差，越小越好")
print(f"NSE: Nash-Sutcliffe效率系数")
print(f"     NSE = 1: 完美预测")
print(f"     NSE = 0: 预测效果等同于使用观测值平均值")
print(f"     NSE < 0: 预测效果比使用平均值还差")
print(f"R²: 决定系数，范围[0,1]，越接近1越好")

# 保存评估结果到CSV
results_df = pd.DataFrame({
    'Metric': ['MAE', 'RMSE', 'NSE', 'R²'],
    'Value': [testing_mae, testing_rmse, testing_nse, testing_r2],
    'Description': [
        '平均绝对误差',
        '均方根误差', 
        'Nash-Sutcliffe效率系数',
        '决定系数'
    ]
})

results_df.to_csv('timemixer_evaluation_results.csv', index=False, encoding='utf-8-sig')
print(f"\n评估结果已保存到: timemixer_evaluation_results.csv")

# 绘制预测结果对比图
plot_prediction_results(true_values_for_testing, timemixer_prediction, 
                        save_path="timemixer_prediction_comparison.png")

print("\n--- 增强版预测流程完成 ---")
print(f"生成的文件:")
print(f"  - timemixer_evaluation_results.csv (评估指标)")
print(f"  - timemixer_prediction_comparison.png (预测对比图)")
print(f"  - 模型文件保存在: {timemixer.saving_path}")
