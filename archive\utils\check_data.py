import pandas as pd

# 检查数据文件结构
file_path = '1964-2017dailyRunoff.csv'

print("🔍 检查数据文件结构")
print("=" * 30)

try:
    # 尝试读取Excel文件
    df = pd.read_excel(file_path)
    print(f"✅ 成功读取文件: {file_path}")
    print(f"📊 数据形状: {df.shape}")
    print(f"📋 列名: {df.columns.tolist()}")
    print(f"\n📝 前5行数据:")
    print(df.head())
    print(f"\n📈 数据类型:")
    print(df.dtypes)
    
except Exception as e:
    print(f"❌ 读取Excel失败: {e}")
    
    # 尝试读取CSV文件
    try:
        df = pd.read_csv(file_path)
        print(f"✅ 成功读取CSV文件: {file_path}")
        print(f"📊 数据形状: {df.shape}")
        print(f"📋 列名: {df.columns.tolist()}")
        print(f"\n📝 前5行数据:")
        print(df.head())
        print(f"\n📈 数据类型:")
        print(df.dtypes)
    except Exception as e2:
        print(f"❌ 读取CSV也失败: {e2}")
