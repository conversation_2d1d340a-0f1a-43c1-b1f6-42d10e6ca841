# PyPOTS Requirements File
# ========================
# Based on PyPOTS official dependencies and requirements

# Core Python packages
numpy>=1.20.0
pandas>=1.3.0
scipy>=1.7.0
scikit-learn>=1.0.0

# Deep Learning Framework
torch>=1.10.0
torchvision>=0.11.0
torchaudio>=0.10.0

# Data Visualization
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# Data Processing
h5py>=3.1.0
openpyxl>=3.0.0
xlrd>=2.0.0

# NLP Dependencies (for TimeLLM and other models)
transformers>=4.0.0
tokenizers>=0.10.0
datasets>=2.0.0

# Build and Packaging
packaging>=20.0
setuptools>=50.0
wheel>=0.37.0

# Development and Testing (optional)
jupyter>=1.0.0
notebook>=6.4.0
ipykernel>=6.0.0
pytest>=6.0.0
pytest-cov>=2.12.0

# Additional utilities
tqdm>=4.60.0
pyyaml>=5.4.0

# PyPOTS itself
pypots>=0.6.0
