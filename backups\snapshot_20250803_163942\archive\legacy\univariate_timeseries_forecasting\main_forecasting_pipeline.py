"""
Main Univariate Time Series Forecasting Pipeline
================================================

This is the main script to run univariate time series forecasting
using TimeMixer++ with your CSV data.

Usage:
    python main_forecasting_pipeline.py --data_path your_data.csv
"""

import argparse
import sys
from pathlib import Path
import pandas as pd
import numpy as np
from typing import Optional, Dict

# Import our modules
from data_loader import UnivariateTimeSeriesLoader, create_sample_data
from model_config import get_univariate_configs, create_custom_univariate_config, print_config_summary
from model_trainer import UnivariateModelTrainer, run_complete_training_pipeline


def validate_csv_file(file_path: str, 
                     date_column: str = 'DATA', 
                     target_column: str = 'runoff') -> bool:
    """
    Validate the CSV file format.
    
    Args:
        file_path: Path to CSV file
        date_column: Expected date column name
        target_column: Expected target column name
        
    Returns:
        True if valid, False otherwise
    """
    try:
        df = pd.read_csv(file_path)
        
        # Check if required columns exist
        if date_column not in df.columns:
            print(f"✗ Error: Column '{date_column}' not found in CSV file")
            print(f"  Available columns: {list(df.columns)}")
            return False
        
        if target_column not in df.columns:
            print(f"✗ Error: Column '{target_column}' not found in CSV file")
            print(f"  Available columns: {list(df.columns)}")
            return False
        
        # Check data types and basic statistics
        print(f"✓ CSV file validation passed")
        print(f"  - Shape: {df.shape}")
        print(f"  - Date column: {date_column}")
        print(f"  - Target column: {target_column}")
        print(f"  - Date range: {df[date_column].iloc[0]} to {df[date_column].iloc[-1]}")
        print(f"  - Target stats: min={df[target_column].min():.2f}, max={df[target_column].max():.2f}, mean={df[target_column].mean():.2f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error validating CSV file: {e}")
        return False


def interactive_config_selection() -> str:
    """
    Interactive configuration selection.
    
    Returns:
        Selected configuration name
    """
    configs = get_univariate_configs()
    
    print("\nAvailable Configurations:")
    print("=" * 30)
    
    config_descriptions = {
        'quick_test': 'Quick test (10→3 steps, 10 epochs) - For testing',
        'short_term': 'Short-term (14→3 steps, 50 epochs) - Fast training',
        'medium_term': 'Medium-term (30→7 steps, 100 epochs) - Balanced',
        'long_term': 'Long-term (90→30 steps, 150 epochs) - High accuracy',
        'daily_to_weekly': 'Daily to weekly (30→7 steps) - Standard forecasting'
    }
    
    for i, (name, desc) in enumerate(config_descriptions.items(), 1):
        print(f"{i}. {name}: {desc}")
    
    while True:
        try:
            choice = input(f"\nSelect configuration (1-{len(configs)}) or press Enter for 'medium_term': ").strip()
            
            if not choice:
                return 'medium_term'
            
            choice_idx = int(choice) - 1
            config_names = list(config_descriptions.keys())
            
            if 0 <= choice_idx < len(config_names):
                selected = config_names[choice_idx]
                print(f"✓ Selected: {selected}")
                return selected
            else:
                print("Invalid choice. Please try again.")
                
        except ValueError:
            print("Invalid input. Please enter a number.")


def main():
    """Main function to run the forecasting pipeline."""
    parser = argparse.ArgumentParser(
        description='Univariate Time Series Forecasting with TimeMixer++',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Use your own CSV file
  python main_forecasting_pipeline.py --data_path your_data.csv
  
  # Use sample data with custom configuration
  python main_forecasting_pipeline.py --use_sample --config medium_term
  
  # Interactive mode
  python main_forecasting_pipeline.py --interactive
        """
    )
    
    parser.add_argument('--data_path', type=str, 
                       help='Path to CSV file with time series data')
    parser.add_argument('--date_column', type=str, default='DATA',
                       help='Name of the date column (default: DATA)')
    parser.add_argument('--target_column', type=str, default='runoff',
                       help='Name of the target variable column (default: runoff)')
    parser.add_argument('--date_format', type=str, default='%Y/%m/%d',
                       help='Date format string (default: %%Y/%%m/%%d)')
    parser.add_argument('--config', type=str, default='medium_term',
                       choices=['quick_test', 'short_term', 'medium_term', 'long_term', 'daily_to_weekly'],
                       help='Predefined configuration to use')
    parser.add_argument('--use_sample', action='store_true',
                       help='Use generated sample data instead of provided CSV')
    parser.add_argument('--interactive', action='store_true',
                       help='Run in interactive mode')
    parser.add_argument('--custom_steps', type=int,
                       help='Custom input sequence length')
    parser.add_argument('--custom_pred_steps', type=int,
                       help='Custom prediction horizon')
    parser.add_argument('--custom_epochs', type=int,
                       help='Custom number of training epochs')
    
    args = parser.parse_args()
    
    print("Univariate Time Series Forecasting Pipeline")
    print("=" * 50)
    print("Using TimeMixer++ from PyPOTS library")
    print()
    
    # Handle data source
    if args.use_sample:
        print("Creating sample data...")
        csv_file_path = create_sample_data("sample_runoff_data.csv")
    elif args.data_path:
        csv_file_path = args.data_path
        if not Path(csv_file_path).exists():
            print(f"✗ Error: File '{csv_file_path}' not found")
            sys.exit(1)
    else:
        print("✗ Error: Please provide --data_path or use --use_sample")
        print("Use --help for more information")
        sys.exit(1)
    
    # Validate CSV file
    if not validate_csv_file(csv_file_path, args.date_column, args.target_column):
        sys.exit(1)
    
    # Handle configuration selection
    if args.interactive:
        config_name = interactive_config_selection()
    else:
        config_name = args.config
    
    # Create custom configuration if custom parameters provided
    custom_config = {}
    if args.custom_steps:
        custom_config['n_steps'] = args.custom_steps
    if args.custom_pred_steps:
        custom_config['n_pred_steps'] = args.custom_pred_steps
    if args.custom_epochs:
        custom_config['epochs'] = args.custom_epochs
    
    # Show configuration summary
    if custom_config:
        config = create_custom_univariate_config(**custom_config)
        print_config_summary(config, "Custom")
    else:
        configs = get_univariate_configs()
        config = configs[config_name]
        print_config_summary(config, config_name.replace('_', ' ').title())
    
    # Confirm before training
    if not args.interactive:
        confirm = input("\nProceed with training? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("Training cancelled.")
            sys.exit(0)
    
    try:
        # Run the complete training pipeline
        results = run_complete_training_pipeline(
            csv_file_path=csv_file_path,
            config_name=config_name if not custom_config else None,
            custom_config=custom_config if custom_config else None
        )
        
        # Print final results summary
        print("\nFinal Results Summary:")
        print("=" * 30)
        metrics = results['evaluation_results']['metrics']
        print(f"Model Performance:")
        for metric_name, value in metrics.items():
            if isinstance(value, float):
                print(f"  - {metric_name}: {value:.4f}")
        
        print(f"\nFiles saved:")
        print(f"  - Model: trained_model.pypots")
        print(f"  - Results: results/")
        print(f"  - Metrics: results/evaluation_metrics.csv")
        print(f"  - Predictions: results/sample_predictions.csv")
        
        print(f"\n✓ Forecasting pipeline completed successfully!")
        
    except KeyboardInterrupt:
        print("\n\nTraining interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n✗ Error during training: {e}")
        print("\nTroubleshooting tips:")
        print("1. Make sure PyPOTS is installed: pip install pypots")
        print("2. Check if your CSV file format matches the expected format")
        print("3. Try using --use_sample to test with sample data")
        print("4. Use --config quick_test for faster testing")
        sys.exit(1)


def demo_mode():
    """
    Run a demonstration with sample data.
    """
    print("Demo Mode: Univariate Time Series Forecasting")
    print("=" * 50)
    
    # Create sample data
    print("1. Creating sample data...")
    csv_file_path = create_sample_data("demo_runoff_data.csv")
    
    # Show available configurations
    print("\n2. Available configurations:")
    configs = get_univariate_configs()
    for name, config in configs.items():
        print(f"   - {name}: {config.n_steps}→{config.n_pred_steps} steps, {config.epochs} epochs")
    
    # Run with quick test configuration
    print("\n3. Running with 'quick_test' configuration...")
    results = run_complete_training_pipeline(
        csv_file_path=csv_file_path,
        config_name='quick_test'
    )
    
    print("\n4. Demo completed! Check the results/ folder for outputs.")


if __name__ == "__main__":
    # Check if running in demo mode
    if len(sys.argv) == 1:
        print("No arguments provided. Running in demo mode...")
        print("For full usage, run: python main_forecasting_pipeline.py --help")
        print()
        demo_mode()
    else:
        main()
