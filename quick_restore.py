"""
快速恢复工具
============

提供快速恢复到任意备份版本的功能，确保实验安全性。
支持从备份、Git提交或代码快照中恢复。
"""

import os
import shutil
import subprocess
import json
from pathlib import Path
from datetime import datetime

class QuickRestore:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.backup_root = self.project_root / "backups"
        self.tracking_dir = self.project_root / ".code_tracking"
        
    def list_available_restores(self):
        """列出所有可用的恢复选项"""
        restore_options = []
        
        # 1. 备份文件
        if self.backup_root.exists():
            for backup_dir in self.backup_root.iterdir():
                if backup_dir.is_dir() and backup_dir.name.startswith("snapshot_"):
                    info_file = backup_dir / "backup_info.json"
                    if info_file.exists():
                        try:
                            with open(info_file, 'r', encoding='utf-8') as f:
                                info = json.load(f)
                            restore_options.append({
                                "type": "backup",
                                "name": backup_dir.name,
                                "timestamp": info["timestamp"],
                                "description": f"项目快照备份 ({info['total_files']} 文件)",
                                "path": backup_dir
                            })
                        except:
                            pass
        
        # 2. Git提交 (如果存在Git仓库)
        if (self.project_root / ".git").exists():
            try:
                result = subprocess.run(
                    ['git', 'log', '--oneline', '-10'],
                    cwd=self.project_root,
                    capture_output=True,
                    text=True
                )
                if result.returncode == 0:
                    for line in result.stdout.strip().split('\n'):
                        if line.strip():
                            commit_hash, *message_parts = line.split()
                            message = ' '.join(message_parts)
                            restore_options.append({
                                "type": "git",
                                "name": commit_hash,
                                "timestamp": None,
                                "description": f"Git提交: {message}",
                                "path": None
                            })
            except:
                pass
        
        # 3. 代码快照 (如果存在追踪系统)
        if self.tracking_dir.exists():
            snapshots_dir = self.tracking_dir / "snapshots"
            if snapshots_dir.exists():
                for snapshot_file in snapshots_dir.iterdir():
                    if snapshot_file.is_file() and snapshot_file.suffix == '.py':
                        # 从文件名解析时间戳
                        parts = snapshot_file.stem.split('_')
                        if len(parts) >= 2:
                            timestamp = '_'.join(parts[-2:])
                            file_name = '_'.join(parts[:-2])
                            restore_options.append({
                                "type": "snapshot",
                                "name": snapshot_file.name,
                                "timestamp": timestamp,
                                "description": f"代码快照: {file_name}.py",
                                "path": snapshot_file
                            })
        
        # 按时间排序
        restore_options.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
        
        return restore_options
    
    def show_restore_options(self, options):
        """显示恢复选项"""
        if not options:
            print("📂 暂无可用的恢复选项")
            return
        
        print("📂 可用的恢复选项:")
        print("="*70)
        
        current_type = None
        for i, option in enumerate(options, 1):
            if option["type"] != current_type:
                current_type = option["type"]
                type_names = {
                    "backup": "🗂️ 项目备份",
                    "git": "🌿 Git提交", 
                    "snapshot": "📄 代码快照"
                }
                print(f"\n{type_names.get(current_type, current_type)}:")
                print("-" * 40)
            
            print(f"{i:2d}. {option['name']}")
            print(f"    {option['description']}")
            if option.get('timestamp'):
                print(f"    时间: {option['timestamp']}")
            print()
    
    def restore_from_backup(self, backup_path):
        """从备份恢复"""
        print(f"🔄 从备份恢复: {backup_path.name}")
        print("="*50)
        
        # 创建当前状态的临时备份
        temp_backup = self.create_temp_backup()
        if temp_backup:
            print(f"✓ 已创建临时备份: {temp_backup}")
        
        try:
            # 恢复代码文件
            code_dir = backup_path / "code"
            if code_dir.exists():
                print("📁 恢复代码文件...")
                for file_path in code_dir.iterdir():
                    if file_path.is_file():
                        dst_file = self.project_root / file_path.name
                        shutil.copy2(file_path, dst_file)
                        print(f"  ✓ {file_path.name}")
            
            # 恢复数据文件 (可选)
            data_dir = backup_path / "data"
            if data_dir.exists():
                restore_data = input("是否恢复数据文件? (y/n): ").lower() == 'y'
                if restore_data:
                    print("📊 恢复数据文件...")
                    for file_path in data_dir.iterdir():
                        if file_path.is_file():
                            dst_file = self.project_root / file_path.name
                            shutil.copy2(file_path, dst_file)
                            print(f"  ✓ {file_path.name}")
            
            print(f"\n✅ 恢复完成!")
            return True
            
        except Exception as e:
            print(f"\n❌ 恢复失败: {e}")
            if temp_backup:
                print(f"可以从临时备份恢复: {temp_backup}")
            return False
    
    def restore_from_git(self, commit_hash):
        """从Git提交恢复"""
        print(f"🔄 从Git提交恢复: {commit_hash}")
        print("="*50)
        
        # 创建当前状态的临时备份
        temp_backup = self.create_temp_backup()
        if temp_backup:
            print(f"✓ 已创建临时备份: {temp_backup}")
        
        try:
            # 检查工作区是否干净
            status_result = subprocess.run(
                ['git', 'status', '--porcelain'],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            
            if status_result.stdout.strip():
                print("⚠ 工作区有未提交的更改")
                save_changes = input("是否先保存当前更改? (y/n): ").lower() == 'y'
                if save_changes:
                    # 创建临时提交
                    subprocess.run(['git', 'add', '.'], cwd=self.project_root)
                    subprocess.run([
                        'git', 'commit', '-m', 
                        f"临时保存 - {datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    ], cwd=self.project_root)
            
            # 恢复到指定提交
            result = subprocess.run(
                ['git', 'checkout', commit_hash, '.'],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print(f"✅ 已恢复到提交: {commit_hash}")
                return True
            else:
                print(f"❌ 恢复失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 恢复失败: {e}")
            return False
    
    def restore_single_file(self, snapshot_path):
        """恢复单个文件快照"""
        print(f"🔄 恢复文件快照: {snapshot_path.name}")
        print("="*50)
        
        # 解析目标文件名
        parts = snapshot_path.stem.split('_')
        if len(parts) >= 2:
            target_file = '_'.join(parts[:-2]) + '.py'
            target_path = self.project_root / target_file
            
            # 备份当前文件
            if target_path.exists():
                backup_name = f"{target_path.stem}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
                backup_path = target_path.parent / backup_name
                shutil.copy2(target_path, backup_path)
                print(f"✓ 当前文件已备份为: {backup_name}")
            
            # 恢复文件
            try:
                shutil.copy2(snapshot_path, target_path)
                print(f"✅ 已恢复文件: {target_file}")
                return True
            except Exception as e:
                print(f"❌ 恢复失败: {e}")
                return False
        else:
            print("❌ 无法解析文件名")
            return False
    
    def create_temp_backup(self):
        """创建临时备份"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_dir = self.project_root / f"temp_backup_{timestamp}"
            temp_dir.mkdir(exist_ok=True)
            
            # 备份核心文件
            core_files = [
                "compatible_training_runner.py",
                "my_parameters.py",
                "run_my_training.py",
                "run_advanced_training.py"
            ]
            
            for file_name in core_files:
                src_file = self.project_root / file_name
                if src_file.exists():
                    dst_file = temp_dir / file_name
                    shutil.copy2(src_file, dst_file)
            
            return temp_dir
        except:
            return None
    
    def interactive_restore(self):
        """交互式恢复"""
        options = self.list_available_restores()
        
        if not options:
            print("📂 暂无可用的恢复选项")
            print("💡 建议先运行备份工具创建备份")
            return
        
        self.show_restore_options(options)
        
        try:
            choice = int(input(f"选择恢复选项 (1-{len(options)}): ")) - 1
            if 0 <= choice < len(options):
                selected = options[choice]
                
                print(f"\n📋 选择的恢复选项:")
                print(f"类型: {selected['type']}")
                print(f"名称: {selected['name']}")
                print(f"描述: {selected['description']}")
                
                confirm = input("\n确认恢复? (y/n): ").lower()
                if confirm != 'y':
                    print("已取消恢复")
                    return
                
                # 执行恢复
                if selected["type"] == "backup":
                    return self.restore_from_backup(selected["path"])
                elif selected["type"] == "git":
                    return self.restore_from_git(selected["name"])
                elif selected["type"] == "snapshot":
                    return self.restore_single_file(selected["path"])
            else:
                print("无效选择")
                return False
                
        except ValueError:
            print("请输入数字")
            return False

def main():
    """主函数"""
    restore_tool = QuickRestore()
    
    print("🎯 TimeMixer快速恢复工具")
    print("="*50)
    print("安全地恢复到任意版本，确保实验可回溯")
    print()
    
    restore_tool.interactive_restore()

if __name__ == "__main__":
    main()
