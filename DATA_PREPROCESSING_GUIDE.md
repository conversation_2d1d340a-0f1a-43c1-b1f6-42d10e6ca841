# TimeMixer数据预处理详细分析

## 📊 数据概览
- **数据源**: 1964-2017dailyRunoff.csv
- **时间跨度**: 1950-2017年 (68年)
- **数据点数**: 24,836天
- **数据类型**: 日径流量 (m³/s)
- **数据范围**: 179-21,200 m³/s

## 🔄 预处理流程

### 1. 数据加载与清洗
```python
df = pd.read_csv('1964-2017dailyRunoff.csv')
df['DATA'] = pd.to_datetime(df['DATA'], format='%Y/%m/%d')
df = df.set_index('DATA')
time_series = df['runoff'].values.astype(np.float32)
```

### 2. 缺失值处理
```python
if np.isnan(time_series).any():
    mean_value = np.nanmean(time_series)
    time_series = np.nan_to_num(time_series, nan=mean_value)
```

### 3. 滑动窗口样本创建
```python
# 参数示例: n_steps=96, n_pred_steps=5
total_sample_len = n_steps + n_pred_steps  # 101
n_samples = len(time_series) - total_sample_len + 1  # 24,736

dataset_X = np.zeros((n_samples, total_sample_len, 1), dtype=np.float32)
for i in range(n_samples):
    dataset_X[i, :, 0] = time_series[i:i + total_sample_len]
```

### 4. 数据划分
```python
train_end = int(n_samples * 0.7)    # 70% 训练
val_end = int(n_samples * 0.85)     # 15% 验证
                                    # 15% 测试

X_input = dataset_X[:, :n_steps, :]      # 输入序列
X_pred = dataset_X[:, n_steps:, :]       # 目标序列
```

## 📈 数据特征

### 统计特征
- **均值**: ~2,184 m³/s
- **标准差**: ~2,156 m³/s  
- **最小值**: 179 m³/s
- **最大值**: 21,200 m³/s

### 时间特征
- **季节性**: 明显的年周期性变化
- **趋势性**: 长期变化趋势
- **极值**: 洪水和枯水期的极端值

## 🎯 不同配置的样本数量

| 配置 | 输入长度 | 预测长度 | 样本数 | 内存需求 |
|------|----------|----------|--------|----------|
| 全数据训练 | 96天 | 5天 | 24,736 | ~9.5MB |
| 季节性预测 | 90天 | 30天 | 24,717 | ~11.9MB |
| 年度预测 | 365天 | 90天 | 24,382 | ~44.5MB |

## ⚡ 性能优化

### 内存优化
- 使用float32减少内存占用
- 批量处理避免内存峰值
- 适当的max_samples限制

### 计算优化  
- 向量化操作替代循环
- 预分配数组空间
- GPU加速训练过程

## 🔧 关键参数

- `n_steps`: 输入序列长度，影响模型能看到的历史信息
- `n_pred_steps`: 预测序列长度，决定预测的时间跨度
- `max_samples`: 样本数量限制，用于快速测试
- `batch_size`: 批次大小，影响训练速度和内存使用

## 📝 注意事项

1. **时间顺序**: 严格按时间顺序划分数据，避免数据泄露
2. **缺失值**: 使用均值填充，可考虑更复杂的插值方法
3. **标准化**: 当前未进行标准化，TimeMixer内部处理
4. **样本重叠**: 滑动窗口创建的样本之间有重叠，这是正常的
