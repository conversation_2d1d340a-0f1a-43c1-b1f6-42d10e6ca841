from pypots.optim import <PERSON>
from pypots.forecasting import Time<PERSON>ixer
from pypots.nn.functional import calc_mae

timemixer = TimeMixer(
    n_steps = physionet2012_dataset["n_steps"] - N_PRED_STEPS,
    n_features = physionet2012_dataset["n_features"],
    n_pred_steps = N_PRED_STEPS,
    n_pred_features = physionet2012_dataset["n_features"],
    term = "short",
    n_layers=2,
    top_k=5,
    d_model=32,
    d_ffn=32,
    moving_avg=25,
    downsampling_window=2,
    downsampling_layers=1,
    use_norm=True,
    dropout=0.1,
    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance
    epochs=10,
    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.
    # You can leave it to defualt as None to disable early stopping.
    patience=3,
    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when
    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.
    optimizer=<PERSON>(lr=1e-3),
    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.
    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.
    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed
    num_workers=0,
    # just leave it to default as None, PyPOTS will automatically assign the best device for you.
    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']
    device=None,
    # set the path for saving tensorboard and trained model files
    saving_path="tutorial_results/forecasting/timemixer",
    # only save the best model after training finished.
    # You can also set it as "better" to save models performing better ever during training.
    model_saving_strategy="best",
)

# train the model on the training set, and validate it on the validating set to select the best model for testing in the next step
timemixer.fit(train_set=dataset_for_FORE_training, val_set=dataset_for_FORE_validating)
# BTTF does not need to run func fits().

# the testing stage
timemixer_results = timemixer.predict(dataset_for_FORE_testing)
timemixer_prediction = timemixer_results["forecasting"]

# calculate the mean absolute error on the ground truth in the forecasting task
testing_mae = calc_mae(
    timemixer_prediction,
    np.nan_to_num(physionet2012_dataset['test_X'][:, -N_PRED_STEPS:]),
    (~np.isnan(physionet2012_dataset['test_X'][:, -N_PRED_STEPS:])).astype(int),
)
print(f"Testing mean absolute error: {testing_mae:.4f}")
