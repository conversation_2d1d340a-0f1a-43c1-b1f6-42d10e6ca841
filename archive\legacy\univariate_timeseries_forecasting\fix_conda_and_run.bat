@echo off
echo ========================================
echo Fixing Conda and Running TimeMixer++
echo ========================================
echo.

echo Step 1: Initializing conda for PowerShell...
call conda init powershell
echo.

echo Step 2: Initializing conda for cmd...
call conda init cmd
echo.

echo Step 3: Refreshing environment variables...
call refreshenv
echo.

echo Step 4: Listing available environments...
call conda env list
echo.

echo Step 5: Activating pypots environment...
call conda activate pypots
echo.

echo Step 6: Verifying Python environment...
python --version
python -c "import pypots; print('PyPOTS version:', pypots.__version__)"
echo.

echo Step 7: Running TimeMixer++ forecasting...
python timemixer_based_on_test.py
echo.

echo ========================================
echo Process completed!
echo ========================================
pause
