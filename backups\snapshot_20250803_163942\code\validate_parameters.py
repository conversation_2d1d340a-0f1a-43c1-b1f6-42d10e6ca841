"""
参数验证脚本
===========

验证所有配置的参数兼容性，特别是moving_avg和n_steps的关系
"""

from my_parameters import get_my_parameters

def validate_parameters():
    """验证所有配置的参数"""
    
    print("🔍 验证TimeMixer参数兼容性")
    print("="*60)
    
    configs = get_my_parameters()
    all_valid = True
    
    for i, config in enumerate(configs, 1):
        print(f"\n配置{i}: {config['name']}")
        print("-" * 40)
        
        n_steps = config['n_steps']
        moving_avg = config['moving_avg']
        n_pred_steps = config['n_pred_steps']
        
        print(f"n_steps: {n_steps}")
        print(f"moving_avg: {moving_avg}")
        print(f"n_pred_steps: {n_pred_steps}")
        
        # 检查关键约束
        issues = []
        
        # 1. moving_avg必须小于n_steps
        if moving_avg >= n_steps:
            issues.append(f"❌ moving_avg ({moving_avg}) 必须小于 n_steps ({n_steps})")
            all_valid = False
        else:
            print(f"✅ moving_avg < n_steps: {moving_avg} < {n_steps}")
        
        # 2. 检查其他可能的约束
        if n_pred_steps <= 0:
            issues.append(f"❌ n_pred_steps ({n_pred_steps}) 必须大于0")
            all_valid = False
        
        if n_steps <= 0:
            issues.append(f"❌ n_steps ({n_steps}) 必须大于0")
            all_valid = False
        
        # 3. 检查合理性
        if moving_avg < 1:
            issues.append(f"⚠️ moving_avg ({moving_avg}) 可能太小")
        
        if moving_avg > n_steps * 0.8:
            issues.append(f"⚠️ moving_avg ({moving_avg}) 可能太接近 n_steps ({n_steps})")
        
        # 显示问题
        if issues:
            for issue in issues:
                print(f"  {issue}")
        else:
            print(f"✅ 所有参数验证通过")
    
    print(f"\n{'='*60}")
    if all_valid:
        print("🎉 所有配置参数验证通过！")
        print("✅ 可以安全运行训练")
    else:
        print("❌ 发现参数问题，需要修复后再运行训练")
    
    return all_valid

def suggest_fixes():
    """建议修复方案"""
    
    print(f"\n💡 TimeMixer参数约束说明:")
    print("-" * 40)
    print("1. moving_avg < n_steps (严格要求)")
    print("2. moving_avg >= 1 (最小值)")
    print("3. 建议 moving_avg <= n_steps * 0.5 (性能最佳)")
    print("4. n_steps 和 n_pred_steps 都必须 > 0")
    
    print(f"\n🔧 常见修复方案:")
    print("-" * 40)
    print("- 如果 moving_avg >= n_steps，减小 moving_avg")
    print("- 推荐值: moving_avg = n_steps // 4 到 n_steps // 2")
    print("- 例如: n_steps=96 → moving_avg=24或48")
    print("- 例如: n_steps=72 → moving_avg=18或36")

def test_fixed_config():
    """测试修复后的配置"""
    
    print(f"\n🧪 测试修复后的配置")
    print("="*60)
    
    from compatible_training_runner import run_compatible_training
    
    # 获取配置1进行快速测试
    configs = get_my_parameters()
    test_config = configs[0].copy()
    
    # 修改为超快速测试
    test_config.update({
        'name': '参数验证测试',
        'epochs': 1,
        'max_samples': 50,
        'batch_size': 8
    })
    
    print(f"测试配置: {test_config['name']}")
    print(f"n_steps: {test_config['n_steps']}")
    print(f"moving_avg: {test_config['moving_avg']}")
    print(f"epochs: {test_config['epochs']}")
    print(f"samples: {test_config['max_samples']}")
    
    try:
        print(f"\n开始测试...")
        result = run_compatible_training(test_config)
        
        if result:
            print(f"✅ 参数验证测试成功!")
            print(f"训练ID: {result['training_id']}")
            return True
        else:
            print(f"❌ 参数验证测试失败!")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    # 验证参数
    valid = validate_parameters()
    
    # 显示修复建议
    suggest_fixes()
    
    if valid:
        # 如果参数都正确，进行快速测试
        print(f"\n" + "="*60)
        test_success = test_fixed_config()
        
        if test_success:
            print(f"\n🎉 所有验证完成！现在可以安全运行完整训练了")
            print(f"🚀 运行命令: python run_my_training.py")
        else:
            print(f"\n⚠️ 参数验证通过，但测试运行失败，请检查其他问题")
    else:
        print(f"\n❌ 请先修复参数问题再进行测试")
