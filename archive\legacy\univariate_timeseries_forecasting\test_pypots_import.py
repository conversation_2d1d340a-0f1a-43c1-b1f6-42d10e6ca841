"""
Test PyPOTS Import
==================

This script tests if PyPOTS can be imported and used.
"""

print("🧪 Testing PyPOTS Import")
print("=" * 30)

# Test 1: Basic import
print("1. Testing basic PyPOTS import...")
try:
    import pypots
    print(f"   ✅ PyPOTS version: {pypots.__version__}")
except Exception as e:
    print(f"   ❌ Basic import failed: {e}")
    exit(1)

# Test 2: TimeMixer import
print("\n2. Testing TimeMixer import...")
try:
    from pypots.forecasting import TimeMixer
    print("   ✅ TimeMixer imported successfully")
except Exception as e:
    print(f"   ❌ TimeMixer import failed: {e}")
    exit(1)

# Test 3: Optimizer import
print("\n3. Testing optimizer import...")
try:
    from pypots.optim import Adam
    print("   ✅ Adam optimizer imported successfully")
except Exception as e:
    print(f"   ❌ Optimizer import failed: {e}")
    exit(1)

# Test 4: Create simple model
print("\n4. Testing model creation...")
try:
    model = TimeMixer(
        n_steps=30,
        n_features=1,
        n_pred_steps=7,
        n_pred_features=1,
        n_layers=1,
        d_model=32,
        epochs=1,
        device='cpu',
        verbose=False
    )
    print("   ✅ TimeMixer model created successfully")
except Exception as e:
    print(f"   ❌ Model creation failed: {e}")
    exit(1)

print("\n🎉 All PyPOTS tests passed!")
print("You can now run the TimeMixer++ implementation.")
