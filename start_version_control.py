"""
版本控制快速启动脚本
====================

一键启动TimeMixer项目的版本控制系统
"""

import os
import sys
from pathlib import Path

def main():
    """主函数"""
    print("🎯 TimeMixer项目版本控制系统")
    print("="*50)
    print("为您的深度学习项目提供完整的代码保存和回溯机制")
    print()
    
    # 检查是否在正确的目录
    current_dir = Path(".")
    expected_files = [
        "compatible_training_runner.py",
        "my_parameters.py",
        "run_my_training.py"
    ]
    
    missing_files = [f for f in expected_files if not (current_dir / f).exists()]
    if missing_files:
        print("⚠️ 警告: 以下核心文件未找到:")
        for f in missing_files:
            print(f"   - {f}")
        print()
        continue_anyway = input("是否继续? (y/n): ").lower() == 'y'
        if not continue_anyway:
            print("已退出")
            return
    
    # 检查版本控制工具是否存在
    vc_manager = current_dir / "version_control_manager.py"
    if not vc_manager.exists():
        print("❌ 版本控制管理器未找到")
        print("请确保 version_control_manager.py 在当前目录中")
        return
    
    print("🚀 启动版本控制管理器...")
    print()
    
    # 启动主管理器
    try:
        os.system(f"python {vc_manager}")
    except KeyboardInterrupt:
        print("\n👋 感谢使用TimeMixer版本控制系统!")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
