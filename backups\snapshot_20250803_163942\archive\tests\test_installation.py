"""
Installation Test Script for TimeMixer++ PyPOTS Integration
===========================================================

This script tests whether all required dependencies are properly installed
and the TimeMixer++ configuration is working correctly.
"""

import sys
import importlib
from typing import List, <PERSON>ple


def test_import(module_name: str, package_name: str = None) -> Tuple[bool, str]:
    """
    Test if a module can be imported.

    Args:
        module_name: Name of the module to import
        package_name: Package name for installation (if different from module)

    Returns:
        Tuple of (success, message)
    """
    try:
        # Special handling for pypots to avoid threadpoolctl issues
        if module_name == "pypots":
            import os
            os.environ["OMP_NUM_THREADS"] = "1"
            os.environ["MKL_NUM_THREADS"] = "1"
            os.environ["NUMEXPR_NUM_THREADS"] = "1"

        importlib.import_module(module_name)
        return True, f"✓ {module_name} imported successfully"
    except ImportError as e:
        pkg_name = package_name or module_name
        return False, f"✗ {module_name} import failed. Install with: pip install {pkg_name}"
    except Exception as e:
        # Handle other errors like threadpoolctl issues
        if "threadpoolctl" in str(e) or "split" in str(e):
            return True, f"⚠ {module_name} imported with warnings (threadpoolctl issue)"
        else:
            pkg_name = package_name or module_name
            return False, f"✗ {module_name} failed with error: {str(e)}"


def test_pytorch_cuda():
    """Test PyTorch CUDA availability."""
    try:
        import torch
        cuda_available = torch.cuda.is_available()
        if cuda_available:
            device_count = torch.cuda.device_count()
            device_name = torch.cuda.get_device_name(0) if device_count > 0 else "Unknown"
            return True, f"✓ CUDA available with {device_count} device(s): {device_name}"
        else:
            return True, "⚠ CUDA not available, will use CPU"
    except Exception as e:
        return False, f"✗ PyTorch CUDA test failed: {e}"


def test_pypots_models():
    """Test PyPOTS model imports."""
    models_to_test = [
        ("pypots.imputation.timemixerpp", "TimeMixerPP imputation"),
        ("pypots.forecasting.timemixer", "TimeMixer forecasting"),
        ("pypots.imputation.saits", "SAITS imputation"),
    ]
    
    results = []
    for module, description in models_to_test:
        try:
            importlib.import_module(module)
            results.append((True, f"✓ {description} available"))
        except ImportError:
            results.append((False, f"⚠ {description} not available"))
    
    return results


def test_configuration():
    """Test the TimeMixer++ configuration."""
    try:
        from timemixer_plus_plus_config import (
            TimeMixerPPConfig, 
            TimeMixerPPModelFactory, 
            get_default_configs
        )
        
        # Test configuration creation
        config = TimeMixerPPConfig()
        configs = get_default_configs()
        
        # Test factory methods (without actually creating models)
        factory = TimeMixerPPModelFactory()
        
        return True, "✓ TimeMixer++ configuration working correctly"
    except Exception as e:
        return False, f"✗ Configuration test failed: {e}"


def run_all_tests():
    """Run all installation tests."""
    print("TimeMixer++ PyPOTS Installation Test")
    print("=" * 50)
    
    # Core dependencies
    print("\n1. Testing Core Dependencies:")
    core_deps = [
        ("numpy", "numpy"),
        ("pandas", "pandas"),
        ("torch", "torch"),
        ("pypots", "pypots"),
    ]
    
    all_passed = True
    for module, package in core_deps:
        success, message = test_import(module, package)
        print(f"   {message}")
        if not success:
            all_passed = False
    
    # PyTorch CUDA test
    print("\n2. Testing PyTorch CUDA:")
    success, message = test_pytorch_cuda()
    print(f"   {message}")
    
    # PyPOTS models test
    print("\n3. Testing PyPOTS Models:")
    model_results = test_pypots_models()
    for success, message in model_results:
        print(f"   {message}")
    
    # Configuration test
    print("\n4. Testing Configuration:")
    success, message = test_configuration()
    print(f"   {message}")
    if not success:
        all_passed = False
    
    # Optional dependencies
    print("\n5. Testing Optional Dependencies:")
    optional_deps = [
        ("matplotlib", "matplotlib"),
        ("sklearn", "scikit-learn"),
        ("scipy", "scipy"),
        ("seaborn", "seaborn"),
    ]
    
    for module, package in optional_deps:
        success, message = test_import(module, package)
        print(f"   {message}")
    
    # Summary
    print("\n" + "=" * 50)
    if all_passed:
        print("✓ All core tests passed! Installation appears to be working correctly.")
        print("\nNext steps:")
        print("1. Run: python timemixer_plus_plus_config.py")
        print("2. Run: python timemixer_plus_plus_example.py")
    else:
        print("✗ Some core tests failed. Please install missing dependencies.")
        print("\nTo install all dependencies:")
        print("pip install -r requirements.txt")
    
    return all_passed


def quick_demo():
    """Run a quick demonstration if all tests pass."""
    print("\n" + "=" * 50)
    print("Quick Configuration Demo:")
    print("=" * 50)
    
    try:
        from timemixer_plus_plus_config import get_default_configs, create_custom_config
        
        # Show default configurations
        configs = get_default_configs()
        print(f"\nAvailable default configurations: {list(configs.keys())}")
        
        # Show medium configuration details
        medium_config = configs['medium']
        print(f"\nMedium configuration details:")
        print(f"  - n_steps: {medium_config.n_steps}")
        print(f"  - n_features: {medium_config.n_features}")
        print(f"  - d_model: {medium_config.d_model}")
        print(f"  - n_layers: {medium_config.n_layers}")
        print(f"  - epochs: {medium_config.epochs}")
        
        # Create custom configuration
        custom_config = create_custom_config(
            n_steps=120,
            n_features=10,
            d_model=384,
            epochs=50
        )
        print(f"\nCustom configuration created:")
        print(f"  - n_steps: {custom_config.n_steps}")
        print(f"  - n_features: {custom_config.n_features}")
        print(f"  - d_model: {custom_config.d_model}")
        
        print("\n✓ Configuration demo completed successfully!")
        
    except Exception as e:
        print(f"\n✗ Configuration demo failed: {e}")


def main():
    """Main function."""
    print(f"Python version: {sys.version}")
    print(f"Python executable: {sys.executable}")
    
    # Run all tests
    all_passed = run_all_tests()
    
    # Run demo if tests passed
    if all_passed:
        quick_demo()
    
    print("\n" + "=" * 50)
    print("Test completed!")
    
    return 0 if all_passed else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
