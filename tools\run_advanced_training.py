"""
高级训练运行器
==============

使用完整TimeMixer参数进行高级训练
支持所有可配置参数的详细控制
"""

from my_parameters import get_my_parameters
from advanced_parameter_configs import ParameterConfigGenerator
from compatible_training_runner import run_compatible_training
import time
import json

def display_parameter_details(config):
    """显示参数详细信息"""
    print(f"\n📋 配置详情: {config['name']}")
    print("="*60)
    
    # 按类别组织参数
    categories = {
        '📊 数据参数': ['n_steps', 'n_pred_steps'],
        '🏗️ 模型架构': ['n_layers', 'd_model', 'd_ffn', 'top_k', 'moving_avg', 'downsampling_window', 'downsampling_layers'],
        '🛡️ 正则化': ['dropout', 'use_norm'],
        '🎯 训练参数': ['epochs', 'batch_size', 'learning_rate', 'patience'],
        '⚙️ 系统参数': ['device', 'num_workers']
    }
    
    for category, param_names in categories.items():
        print(f"\n{category}:")
        for param_name in param_names:
            if param_name in config:
                value = config[param_name]
                print(f"  {param_name}: {value}")
    print()

def run_single_config():
    """运行单个配置"""
    configs = get_my_parameters()
    
    print("📋 可用配置:")
    for i, config in enumerate(configs, 1):
        print(f"{i}. {config['name']}")
        print(f"   序列: {config['n_steps']}→{config['n_pred_steps']}")
        print(f"   模型: {config['n_layers']}层, {config['d_model']}维度")
        print(f"   训练: {config['epochs']}轮")
        print()
    
    while True:
        try:
            choice = int(input(f"选择配置 (1-{len(configs)}): ")) - 1
            if 0 <= choice < len(configs):
                selected_config = configs[choice]
                break
            else:
                print("无效选择，请重试")
        except ValueError:
            print("请输入数字")
    
    # 显示详细参数
    display_parameter_details(selected_config)
    
    # 确认运行
    confirm = input("确认运行此配置? (y/n): ").lower()
    if confirm != 'y':
        print("已取消")
        return
    
    print(f"\n🚀 开始训练: {selected_config['name']}")
    start_time = time.time()
    
    result = run_compatible_training(selected_config)
    
    end_time = time.time()
    duration = end_time - start_time
    
    if result:
        print(f"\n✅ 训练完成! (耗时: {duration:.1f}秒)")
        print(f"训练ID: {result['training_id']}")
        print(f"MAE: {result['mae']:.6f}")
        print(f"RMSE: {result['rmse']:.6f}")
        print(f"NSE: {result['nse']:.6f}")
        print(f"R²: {result['r2']:.6f}")
    else:
        print(f"\n❌ 训练失败! (耗时: {duration:.1f}秒)")

def run_batch_training():
    """运行批量训练"""
    configs = get_my_parameters()
    
    print(f"🚀 批量训练模式")
    print(f"共有 {len(configs)} 个配置")
    print()
    
    for i, config in enumerate(configs, 1):
        print(f"{i}. {config['name']}")
        display_parameter_details(config)
    
    confirm = input(f"确认运行所有 {len(configs)} 个配置? (y/n): ").lower()
    if confirm != 'y':
        print("已取消")
        return
    
    print(f"\n🚀 开始批量训练...")
    results = []
    total_start_time = time.time()
    
    for i, config in enumerate(configs, 1):
        print(f"\n{'='*60}")
        print(f"进度: {i}/{len(configs)} - {config['name']}")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        try:
            result = run_compatible_training(config)
            end_time = time.time()
            duration = end_time - start_time
            
            if result:
                results.append({
                    'config_name': config['name'],
                    'training_id': result['training_id'],
                    'mae': result['mae'],
                    'rmse': result['rmse'],
                    'nse': result['nse'],
                    'r2': result['r2'],
                    'duration': duration,
                    'parameters': config
                })
                print(f"✅ {config['name']} 完成 (耗时: {duration:.1f}秒)")
                print(f"   MAE: {result['mae']:.6f}, R²: {result['r2']:.6f}")
            else:
                print(f"❌ {config['name']} 失败 (耗时: {duration:.1f}秒)")
                
        except Exception as e:
            print(f"❌ {config['name']} 异常: {e}")
        
        # 间隔时间
        if i < len(configs):
            print("等待 3 秒后继续...")
            time.sleep(3)
    
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    # 显示结果汇总
    if results:
        print(f"\n📊 批量训练结果汇总 (总耗时: {total_duration:.1f}秒):")
        print("="*100)
        print(f"{'配置名称':<25} {'训练ID':<12} {'MAE':<12} {'RMSE':<12} {'NSE':<8} {'R²':<8} {'耗时(秒)':<8}")
        print("-" * 100)
        
        for result in results:
            print(f"{result['config_name']:<25} {result['training_id']:<12} "
                  f"{result['mae']:<12.6f} {result['rmse']:<12.6f} "
                  f"{result['nse']:<8.4f} {result['r2']:<8.4f} {result['duration']:<8.1f}")
        
        # 找出最佳结果
        best_mae = min(results, key=lambda x: x['mae'])
        best_r2 = max(results, key=lambda x: x['r2'])
        fastest = min(results, key=lambda x: x['duration'])
        
        print(f"\n🏆 最佳结果:")
        print(f"最低 MAE: {best_mae['config_name']} (MAE: {best_mae['mae']:.6f})")
        print(f"最高 R²: {best_r2['config_name']} (R²: {best_r2['r2']:.6f})")
        print(f"最快训练: {fastest['config_name']} (耗时: {fastest['duration']:.1f}秒)")
        
        # 保存详细结果
        with open('advanced_training_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"\n📈 详细结果已保存到: advanced_training_results.json")
        print(f"📈 汇总结果已保存到: timemixer_evaluation_results.csv")
    else:
        print(f"\n❌ 没有成功的训练结果")

def show_parameter_guide():
    """显示参数调优指南"""
    generator = ParameterConfigGenerator()
    generator.print_parameter_guide()

def main():
    """主函数"""
    print("🎯 TimeMixer++ 高级训练运行器")
    print("="*50)
    print("包含完整参数控制的高级训练系统")
    print()
    
    while True:
        print("选择操作:")
        print("1. 运行单个配置")
        print("2. 运行批量训练")
        print("3. 查看参数调优指南")
        print("4. 查看配置详情")
        print("5. 退出")
        
        choice = input("请选择 (1-5): ").strip()
        
        if choice == "1":
            run_single_config()
        elif choice == "2":
            run_batch_training()
        elif choice == "3":
            show_parameter_guide()
        elif choice == "4":
            configs = get_my_parameters()
            for config in configs:
                display_parameter_details(config)
        elif choice == "5":
            print("再见!")
            break
        else:
            print("无效选择，请重试")
        
        print("\n" + "="*50)

if __name__ == "__main__":
    main()
