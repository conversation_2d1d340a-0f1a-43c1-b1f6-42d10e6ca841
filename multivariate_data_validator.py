"""
多变量数据验证器
================

验证多变量气象数据的质量，检查数据完整性，
并提供数据统计信息用于TimeMixer多变量训练。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

class MultivariateDataValidator:
    def __init__(self, data_file='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        self.data_file = data_file
        # 基于LightGBM特征重要性分析选择的5个最重要特征
        self.input_features = ['RUNOFF', 'VISIB', 'SLP', 'PRCP', 'DEWP']
        self.target_feature = 'RUNOFF'
        self.df = None
        
    def load_and_validate_data(self):
        """加载并验证数据"""
        print("🔍 加载和验证多变量数据")
        print("="*50)
        
        try:
            self.df = pd.read_csv(self.data_file)
            print(f"✅ 数据文件加载成功: {self.data_file}")
            print(f"📊 数据形状: {self.df.shape}")
            
            # 检查列名
            expected_columns = ['DATE'] + [self.target_feature] + self.input_features
            missing_columns = [col for col in expected_columns if col not in self.df.columns]
            
            if missing_columns:
                print(f"❌ 缺失列: {missing_columns}")
                return False
            else:
                print(f"✅ 所有必需列都存在")
            
            # 转换日期列
            self.df['DATE'] = pd.to_datetime(self.df['DATE'], format='%Y/%m/%d')
            self.df = self.df.set_index('DATE')
            
            print(f"📅 数据时间范围: {self.df.index.min()} 到 {self.df.index.max()}")
            print(f"📅 数据天数: {len(self.df)} 天")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def check_data_quality(self):
        """检查数据质量"""
        print("\n🔍 数据质量检查")
        print("="*30)
        
        # 检查缺失值
        missing_counts = self.df.isnull().sum()
        print("缺失值统计:")
        for feature in [self.target_feature] + self.input_features:
            count = missing_counts[feature]
            percentage = (count / len(self.df)) * 100
            status = "✅" if count == 0 else "⚠️" if percentage < 5 else "❌"
            print(f"  {status} {feature}: {count} ({percentage:.2f}%)")
        
        # 检查异常值
        print("\n异常值检查 (超出3倍标准差):")
        for feature in [self.target_feature] + self.input_features:
            data = self.df[feature].dropna()
            mean_val = data.mean()
            std_val = data.std()
            outliers = data[(data < mean_val - 3*std_val) | (data > mean_val + 3*std_val)]
            percentage = (len(outliers) / len(data)) * 100
            status = "✅" if percentage < 1 else "⚠️" if percentage < 5 else "❌"
            print(f"  {status} {feature}: {len(outliers)} ({percentage:.2f}%)")
    
    def show_data_statistics(self):
        """显示数据统计信息"""
        print("\n📊 数据统计信息")
        print("="*40)
        
        # 基本统计
        stats = self.df[[self.target_feature] + self.input_features].describe()
        print("\n基本统计:")
        print(stats.round(2))
        
        # 相关性分析
        print(f"\n🔗 与目标变量 {self.target_feature} 的相关性:")
        all_correlations = self.df[self.input_features].corr()[self.target_feature]

        # 按绝对值排序但保留原始符号
        correlations_abs = all_correlations.abs().sort_values(ascending=False)

        for feature in correlations_abs.index:
            if feature != self.target_feature:  # 跳过自相关
                corr = all_correlations[feature]
                strength = "强" if abs(corr) > 0.7 else "中" if abs(corr) > 0.4 else "弱"
                direction = "正" if corr > 0 else "负"
                print(f"  {feature}: {corr:.3f} ({strength}{direction}相关)")
    
    def analyze_temporal_patterns(self):
        """分析时间模式"""
        print("\n📅 时间模式分析")
        print("="*30)
        
        # 添加时间特征
        self.df['year'] = self.df.index.year
        self.df['month'] = self.df.index.month
        self.df['day_of_year'] = self.df.index.dayofyear
        
        # 年度趋势
        yearly_stats = self.df.groupby('year')[self.target_feature].agg(['mean', 'std', 'min', 'max'])
        print(f"\n年度 {self.target_feature} 统计:")
        print(f"  平均值变化: {yearly_stats['mean'].min():.1f} - {yearly_stats['mean'].max():.1f}")
        print(f"  标准差变化: {yearly_stats['std'].min():.1f} - {yearly_stats['std'].max():.1f}")
        
        # 季节性模式
        monthly_stats = self.df.groupby('month')[self.target_feature].mean()
        print(f"\n月度 {self.target_feature} 模式:")
        print(f"  最高月份: {monthly_stats.idxmax()}月 ({monthly_stats.max():.1f})")
        print(f"  最低月份: {monthly_stats.idxmin()}月 ({monthly_stats.min():.1f})")
        print(f"  季节性变化幅度: {monthly_stats.max() - monthly_stats.min():.1f}")
    
    def check_timemixer_compatibility(self):
        """检查TimeMixer兼容性"""
        print("\n🤖 TimeMixer兼容性检查")
        print("="*40)
        
        # 数据长度检查
        data_length = len(self.df)
        print(f"数据长度: {data_length} 天")
        
        # 不同配置的样本数量估算
        configs = [
            ("快速验证", 30, 7),
            ("季节性预测", 60, 14),
            ("长期预测", 180, 30),
            ("全数据训练", 90, 15)
        ]
        
        print("\n各配置可创建的样本数:")
        for name, n_steps, n_pred_steps in configs:
            total_len = n_steps + n_pred_steps
            n_samples = max(0, data_length - total_len + 1)
            status = "✅" if n_samples > 1000 else "⚠️" if n_samples > 100 else "❌"
            print(f"  {status} {name} ({n_steps}→{n_pred_steps}): {n_samples} 样本")
        
        # 特征数量
        n_features = len(self.input_features)
        print(f"\n特征配置:")
        print(f"  输入特征数: {n_features} (基于LightGBM特征重要性选择)")
        print(f"  输出特征数: 1 ({self.target_feature} 未来值)")
        print(f"  特征列表: {', '.join(self.input_features)}")
        print(f"  选择依据: LightGBM分析，R²=0.9504")
        print(f"  说明: 精选最重要的5个特征，提高训练效率")
        
        # 内存估算
        print(f"\n内存需求估算 (以90天输入，15天输出为例):")
        sample_size = 90 * n_features * 4  # float32
        for n_samples in [1000, 5000, 10000]:
            memory_mb = (n_samples * sample_size) / (1024 * 1024)
            status = "✅" if memory_mb < 500 else "⚠️" if memory_mb < 2000 else "❌"
            print(f"  {status} {n_samples} 样本: ~{memory_mb:.1f} MB")
    
    def generate_data_report(self):
        """生成数据报告"""
        print("\n📋 数据报告生成")
        print("="*30)
        
        report = {
            "数据文件": self.data_file,
            "数据形状": self.df.shape,
            "时间范围": f"{self.df.index.min()} 到 {self.df.index.max()}",
            "总天数": len(self.df),
            "输入特征": self.input_features,
            "输出目标": self.target_feature,
            "缺失值": self.df.isnull().sum().to_dict(),
            "基本统计": self.df.describe().to_dict()
        }
        
        # 保存报告
        import json
        report_file = "multivariate_data_report.json"
        
        # 转换不可序列化的对象
        for key, value in report.items():
            if hasattr(value, 'to_dict'):
                report[key] = value.to_dict()
            elif isinstance(value, (pd.Timestamp, np.datetime64)):
                report[key] = str(value)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"✅ 数据报告已保存: {report_file}")
        
        return report
    
    def run_full_validation(self):
        """运行完整验证"""
        print("🎯 多变量数据完整验证")
        print("="*50)
        
        if not self.load_and_validate_data():
            return False
        
        self.check_data_quality()
        self.show_data_statistics()
        self.analyze_temporal_patterns()
        self.check_timemixer_compatibility()
        self.generate_data_report()
        
        print("\n✅ 数据验证完成!")
        print("\n💡 建议:")
        print("1. 数据质量良好，可以进行TimeMixer训练")
        print("2. 建议从快速验证配置开始")
        print("3. 注意监控训练过程中的内存使用")
        print("4. 可以根据相关性分析调整特征选择")
        
        return True

def main():
    """主函数"""
    validator = MultivariateDataValidator()
    
    print("🎯 TimeMixer多变量数据验证器")
    print("="*50)
    
    if not Path(validator.data_file).exists():
        print(f"❌ 数据文件不存在: {validator.data_file}")
        print("请确保数据文件在当前目录中")
        return
    
    validator.run_full_validation()

if __name__ == "__main__":
    main()
