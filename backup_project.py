"""
TimeMixer项目备份工具
====================

自动创建项目代码的时间戳备份，确保代码安全和版本可追溯性。
"""

import os
import shutil
import datetime
import json
import hashlib
from pathlib import Path

class ProjectBackup:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.backup_root = self.project_root / "backups"
        self.backup_root.mkdir(exist_ok=True)
        
        # 核心文件列表 - 需要备份的重要文件
        self.core_files = [
            "compatible_training_runner.py",
            "my_parameters.py", 
            "run_my_training.py",
            "run_advanced_training.py",
            "debug_timemixer.py",
            "test_normalization.py",
            "validate_parameters.py",
            "requirements.txt",
            "PROJECT_GUIDE.md",
            "README.md",
            "DATA_PREPROCESSING_GUIDE.md"
        ]
        
        # 数据文件
        self.data_files = [
            "1964-2017dailyRunoff.csv",
            "1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv",
            "timemixer_evaluation_results.csv"
        ]
        
        # 需要排除的文件/目录
        self.exclude_patterns = [
            "__pycache__",
            "*.pyc",
            "*.pyo", 
            ".git",
            "backups",
            "*.log",
            "*.tmp"
        ]

    def get_file_hash(self, file_path):
        """计算文件的MD5哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except:
            return None

    def create_backup_info(self, backup_dir, file_list):
        """创建备份信息文件"""
        backup_info = {
            "timestamp": datetime.datetime.now().isoformat(),
            "backup_type": "project_snapshot",
            "total_files": len(file_list),
            "files": {}
        }
        
        for file_path in file_list:
            if file_path.exists():
                backup_info["files"][str(file_path)] = {
                    "size": file_path.stat().st_size,
                    "modified": datetime.datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                    "hash": self.get_file_hash(file_path)
                }
        
        info_file = backup_dir / "backup_info.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(backup_info, f, ensure_ascii=False, indent=2)
        
        return backup_info

    def create_snapshot_backup(self):
        """创建完整项目快照备份"""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"snapshot_{timestamp}"
        backup_dir = self.backup_root / backup_name
        backup_dir.mkdir(exist_ok=True)
        
        print(f"🔄 创建项目快照备份: {backup_name}")
        print("="*50)
        
        # 备份核心代码文件
        code_dir = backup_dir / "code"
        code_dir.mkdir(exist_ok=True)
        
        backed_up_files = []
        
        print("📁 备份核心代码文件...")
        for file_name in self.core_files:
            src_file = self.project_root / file_name
            if src_file.exists():
                dst_file = code_dir / file_name
                shutil.copy2(src_file, dst_file)
                backed_up_files.append(src_file)
                print(f"  ✓ {file_name}")
            else:
                print(f"  ⚠ {file_name} (文件不存在)")
        
        # 备份数据文件
        data_dir = backup_dir / "data"
        data_dir.mkdir(exist_ok=True)
        
        print("\n📊 备份数据文件...")
        for file_name in self.data_files:
            src_file = self.project_root / file_name
            if src_file.exists():
                dst_file = data_dir / file_name
                shutil.copy2(src_file, dst_file)
                backed_up_files.append(src_file)
                print(f"  ✓ {file_name}")
            else:
                print(f"  ⚠ {file_name} (文件不存在)")
        
        # 备份archive目录中的重要文件
        archive_src = self.project_root / "archive"
        if archive_src.exists():
            archive_dst = backup_dir / "archive"
            print(f"\n📦 备份archive目录...")
            shutil.copytree(archive_src, archive_dst, ignore=shutil.ignore_patterns(*self.exclude_patterns))
            print(f"  ✓ archive目录已备份")
        
        # 创建备份信息文件
        backup_info = self.create_backup_info(backup_dir, backed_up_files)
        
        print(f"\n✅ 备份完成!")
        print(f"📍 备份位置: {backup_dir}")
        print(f"📊 备份统计:")
        print(f"  - 总文件数: {backup_info['total_files']}")
        print(f"  - 备份时间: {backup_info['timestamp']}")
        
        return backup_dir, backup_info

    def list_backups(self):
        """列出所有可用的备份"""
        if not self.backup_root.exists():
            print("📂 暂无备份文件")
            return []
        
        backups = []
        for backup_dir in self.backup_root.iterdir():
            if backup_dir.is_dir() and backup_dir.name.startswith("snapshot_"):
                info_file = backup_dir / "backup_info.json"
                if info_file.exists():
                    try:
                        with open(info_file, 'r', encoding='utf-8') as f:
                            info = json.load(f)
                        backups.append({
                            "name": backup_dir.name,
                            "path": backup_dir,
                            "info": info
                        })
                    except:
                        pass
        
        # 按时间排序
        backups.sort(key=lambda x: x["info"]["timestamp"], reverse=True)
        
        if backups:
            print("📂 可用备份列表:")
            print("="*60)
            for i, backup in enumerate(backups, 1):
                info = backup["info"]
                print(f"{i}. {backup['name']}")
                print(f"   时间: {info['timestamp']}")
                print(f"   文件数: {info['total_files']}")
                print()
        else:
            print("📂 暂无备份文件")
        
        return backups

    def restore_from_backup(self, backup_name):
        """从备份恢复文件"""
        backup_dir = self.backup_root / backup_name
        if not backup_dir.exists():
            print(f"❌ 备份不存在: {backup_name}")
            return False
        
        print(f"🔄 从备份恢复: {backup_name}")
        print("="*50)
        
        # 恢复代码文件
        code_dir = backup_dir / "code"
        if code_dir.exists():
            print("📁 恢复代码文件...")
            for file_path in code_dir.iterdir():
                if file_path.is_file():
                    dst_file = self.project_root / file_path.name
                    shutil.copy2(file_path, dst_file)
                    print(f"  ✓ {file_path.name}")
        
        # 恢复数据文件
        data_dir = backup_dir / "data"
        if data_dir.exists():
            print("\n📊 恢复数据文件...")
            for file_path in data_dir.iterdir():
                if file_path.is_file():
                    dst_file = self.project_root / file_path.name
                    shutil.copy2(file_path, dst_file)
                    print(f"  ✓ {file_path.name}")
        
        print(f"\n✅ 恢复完成!")
        return True

def main():
    """主函数"""
    backup_tool = ProjectBackup()
    
    print("🎯 TimeMixer项目备份工具")
    print("="*50)
    
    while True:
        print("\n选择操作:")
        print("1. 创建项目快照备份")
        print("2. 查看备份列表")
        print("3. 从备份恢复")
        print("4. 退出")
        
        choice = input("请选择 (1-4): ").strip()
        
        if choice == "1":
            backup_tool.create_snapshot_backup()
            
        elif choice == "2":
            backup_tool.list_backups()
            
        elif choice == "3":
            backups = backup_tool.list_backups()
            if backups:
                try:
                    idx = int(input(f"选择备份编号 (1-{len(backups)}): ")) - 1
                    if 0 <= idx < len(backups):
                        backup_name = backups[idx]["name"]
                        confirm = input(f"确认恢复备份 '{backup_name}'? (y/n): ").lower()
                        if confirm == 'y':
                            backup_tool.restore_from_backup(backup_name)
                        else:
                            print("已取消恢复")
                    else:
                        print("无效选择")
                except ValueError:
                    print("请输入数字")
            
        elif choice == "4":
            print("再见!")
            break
            
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()
