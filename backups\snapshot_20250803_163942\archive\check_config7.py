"""
检查配置7参数
=============

验证配置7的epochs参数是否正确
"""

from my_parameters import get_my_parameters

def check_config7():
    """检查配置7参数"""
    
    configs = get_my_parameters()
    config7 = configs[-1]  # 最后一个配置就是配置7
    
    print("🔍 检查配置7参数")
    print("="*50)
    print(f"配置名称: {config7['name']}")
    print(f"epochs: {config7['epochs']}")
    print(f"patience: {config7['patience']}")
    print(f"n_pred_steps: {config7['n_pred_steps']}")
    print(f"learning_rate: {config7['learning_rate']}")
    print(f"device: {config7['device']}")
    print()
    
    if config7['epochs'] == 500:
        print("✅ epochs参数正确设置为500")
    else:
        print(f"❌ epochs参数错误: 期望500，实际{config7['epochs']}")
    
    if config7['patience'] == 30:
        print("✅ patience参数正确设置为30")
    else:
        print(f"❌ patience参数错误: 期望30，实际{config7['patience']}")
    
    print(f"\n📋 完整配置:")
    for key, value in config7.items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    check_config7()
