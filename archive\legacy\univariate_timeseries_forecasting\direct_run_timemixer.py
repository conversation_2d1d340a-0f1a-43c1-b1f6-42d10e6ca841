"""
Direct Run TimeMixer++ 
======================

This script directly runs TimeMixer++ by setting the Python path to the pypots environment.
"""

import sys
import os
import subprocess

def find_pypots_python():
    """Find the Python executable in the pypots environment."""
    possible_paths = [
        r"D:\APPS\Anaconda\envs\pypots\python.exe",
        r"C:\Users\<USER>\Anaconda3\envs\pypots\python.exe",
        r"C:\Users\<USER>\miniconda3\envs\pypots\python.exe",
        r"C:\Anaconda3\envs\pypots\python.exe",
        r"C:\miniconda3\envs\pypots\python.exe"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return None

def run_timemixer_directly():
    """Run TimeMixer++ directly using the pypots Python executable."""
    print("🔍 Finding PyPOTS Python Environment")
    print("=" * 50)
    
    pypots_python = find_pypots_python()
    
    if pypots_python:
        print(f"✅ Found PyPOTS Python: {pypots_python}")
    else:
        print("❌ Could not find PyPOTS Python executable")
        print("Please check if the pypots environment was created successfully")
        return False
    
    # Test the environment
    print(f"\n🧪 Testing PyPOTS Environment")
    test_cmd = f'"{pypots_python}" -c "import pypots; print(\'PyPOTS version:\', pypots.__version__)"'
    
    try:
        result = subprocess.run(test_cmd, shell=True, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print(f"✅ Environment test passed:")
            print(f"   {result.stdout.strip()}")
        else:
            print(f"❌ Environment test failed:")
            print(f"   {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Environment test error: {e}")
        return False
    
    # Run TimeMixer++
    print(f"\n🚀 Running TimeMixer++ Forecasting")
    print("=" * 50)
    
    timemixer_cmd = f'"{pypots_python}" timemixer_based_on_test.py'
    
    try:
        print("Starting TimeMixer++ execution...")
        print("This may take several minutes...")
        print()
        
        # Run with real-time output
        process = subprocess.Popen(
            timemixer_cmd,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # Print output in real-time
        for line in process.stdout:
            print(line.rstrip())
        
        process.wait()
        
        if process.returncode == 0:
            print("\n🎉 TimeMixer++ completed successfully!")
            return True
        else:
            print(f"\n❌ TimeMixer++ failed with return code: {process.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ Error running TimeMixer++: {e}")
        return False

def main():
    """Main function."""
    print("🌊 DIRECT TIMEMIXER++ RUNNER")
    print("=" * 60)
    print("This script bypasses conda activation issues by directly")
    print("using the Python executable from the pypots environment.")
    print()
    
    success = run_timemixer_directly()
    
    if success:
        print("\n✅ All operations completed successfully!")
        print("\nGenerated files should include:")
        print("  - timemixer_runoff_predictions.png")
        print("  - timemixer_predictions.csv")
        print("  - timemixer_summary.csv")
    else:
        print("\n❌ Operation failed!")
        print("\nTroubleshooting:")
        print("1. Make sure the pypots environment was created")
        print("2. Check if PyPOTS is properly installed")
        print("3. Verify the Python path is correct")

if __name__ == "__main__":
    main()
