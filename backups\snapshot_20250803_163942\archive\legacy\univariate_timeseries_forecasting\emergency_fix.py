"""
Emergency Fix for Anaconda Metadata Issues
==========================================

This script provides an emergency fix for the Anaconda metadata corruption
that's preventing PyPOTS from working.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def fix_anaconda_metadata():
    """Fix Anaconda metadata corruption."""
    print("🚨 EMERGENCY FIX FOR ANACONDA METADATA")
    print("=" * 50)
    
    # Get Anaconda path
    anaconda_path = Path(sys.executable).parent.parent
    site_packages = anaconda_path / "Lib" / "site-packages"
    
    print(f"Anaconda path: {anaconda_path}")
    print(f"Site-packages: {site_packages}")
    
    if not site_packages.exists():
        print("❌ Cannot find site-packages directory")
        return False
    
    # Find and fix corrupted .dist-info directories
    print("\n🔍 Finding corrupted .dist-info directories...")
    
    corrupted_dirs = []
    for item in site_packages.iterdir():
        if item.is_dir() and item.name.endswith('.dist-info'):
            metadata_file = item / "METADATA"
            if metadata_file.exists():
                try:
                    with open(metadata_file, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        if 'Name:' not in content or content.strip() == '':
                            corrupted_dirs.append(item)
                except:
                    corrupted_dirs.append(item)
    
    print(f"Found {len(corrupted_dirs)} corrupted directories")
    
    # Backup and remove corrupted directories
    if corrupted_dirs:
        backup_dir = site_packages / "_corrupted_backup"
        backup_dir.mkdir(exist_ok=True)
        
        print(f"\n🗂️  Moving corrupted directories to backup...")
        for corrupt_dir in corrupted_dirs[:10]:  # Limit to first 10
            try:
                backup_path = backup_dir / corrupt_dir.name
                if backup_path.exists():
                    shutil.rmtree(backup_path)
                shutil.move(str(corrupt_dir), str(backup_path))
                print(f"   Moved: {corrupt_dir.name}")
            except Exception as e:
                print(f"   Failed to move {corrupt_dir.name}: {e}")
    
    return True

def minimal_pypots_install():
    """Install PyPOTS with minimal dependencies."""
    print("\n📦 MINIMAL PYPOTS INSTALLATION")
    print("=" * 50)
    
    commands = [
        # Install only essential packages
        "pip install --no-deps torch",
        "pip install --no-deps numpy", 
        "pip install --no-deps pandas",
        "pip install --no-deps matplotlib",
        "pip install --no-deps scikit-learn",
        
        # Try to install PyPOTS without dependencies
        "pip install --no-deps pypots",
    ]
    
    for cmd in commands:
        print(f"Running: {cmd}")
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)
            if result.returncode == 0:
                print("   ✅ Success")
            else:
                print(f"   ⚠️  Warning: {result.stderr[:100]}...")
        except Exception as e:
            print(f"   ❌ Error: {e}")

def create_minimal_timemixer():
    """Create a minimal TimeMixer implementation."""
    print("\n🤖 CREATING MINIMAL TIMEMIXER")
    print("=" * 50)
    
    minimal_code = '''
"""
Minimal TimeMixer Implementation
===============================
"""

import numpy as np
import torch
import torch.nn as nn
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error

class MinimalTimeMixer:
    """Minimal TimeMixer-like implementation using PyTorch."""
    
    def __init__(self, n_steps=30, n_pred_steps=7, d_model=128, n_layers=2):
        self.n_steps = n_steps
        self.n_pred_steps = n_pred_steps
        self.d_model = d_model
        self.n_layers = n_layers
        self.scaler = StandardScaler()
        self.model = None
        self.device = torch.device('cpu')
        
    def _create_model(self):
        """Create a simple LSTM-based model."""
        class SimpleLSTM(nn.Module):
            def __init__(self, input_size, hidden_size, num_layers, output_size):
                super(SimpleLSTM, self).__init__()
                self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
                self.fc = nn.Linear(hidden_size, output_size)
                
            def forward(self, x):
                lstm_out, _ = self.lstm(x)
                output = self.fc(lstm_out[:, -1, :])  # Use last output
                return output.unsqueeze(-1)  # Add feature dimension
        
        return SimpleLSTM(1, self.d_model, self.n_layers, self.n_pred_steps)
    
    def fit(self, train_data, val_data=None, epochs=20):
        """Train the model."""
        print(f"Training minimal TimeMixer for {epochs} epochs...")
        
        X_train = train_data['X']
        
        # Normalize data
        X_flat = X_train.reshape(-1, 1)
        X_normalized = self.scaler.fit_transform(X_flat)
        X_train_norm = X_normalized.reshape(X_train.shape)
        
        # Convert to PyTorch tensors
        X_tensor = torch.FloatTensor(X_train_norm).to(self.device)
        
        # Create model
        self.model = self._create_model().to(self.device)
        optimizer = torch.optim.Adam(self.model.parameters(), lr=0.001)
        criterion = nn.MSELoss()
        
        # Simple training loop
        self.model.train()
        for epoch in range(epochs):
            optimizer.zero_grad()
            
            # Create targets (next n_pred_steps values)
            targets = []
            for i in range(len(X_train_norm) - self.n_pred_steps):
                target = X_train_norm[i + 1:i + 1 + self.n_pred_steps, -1, 0]
                if len(target) == self.n_pred_steps:
                    targets.append(target)
            
            if len(targets) == 0:
                continue
                
            targets = torch.FloatTensor(targets).unsqueeze(-1).to(self.device)
            inputs = X_tensor[:len(targets)]
            
            outputs = self.model(inputs)
            loss = criterion(outputs, targets)
            
            loss.backward()
            optimizer.step()
            
            if epoch % 5 == 0:
                print(f"   Epoch {epoch}, Loss: {loss.item():.4f}")
    
    def predict(self, test_data):
        """Make predictions."""
        print("Making predictions...")
        
        X_test = test_data['X']
        
        # Normalize test data
        X_flat = X_test.reshape(-1, 1)
        X_normalized = self.scaler.transform(X_flat)
        X_test_norm = X_normalized.reshape(X_test.shape)
        
        # Convert to tensor
        X_tensor = torch.FloatTensor(X_test_norm).to(self.device)
        
        # Make predictions
        self.model.eval()
        with torch.no_grad():
            predictions = self.model(X_tensor)
        
        # Convert back to numpy and denormalize
        pred_np = predictions.cpu().numpy()
        
        # Denormalize
        pred_flat = pred_np.reshape(-1, 1)
        pred_denorm = self.scaler.inverse_transform(pred_flat)
        pred_final = pred_denorm.reshape(pred_np.shape)
        
        return {'forecasting': pred_final}
    
    def save(self, path):
        """Save model."""
        if self.model:
            torch.save({
                'model_state': self.model.state_dict(),
                'scaler': self.scaler,
                'config': {
                    'n_steps': self.n_steps,
                    'n_pred_steps': self.n_pred_steps,
                    'd_model': self.d_model,
                    'n_layers': self.n_layers
                }
            }, path)
            print(f"Model saved to {path}")
'''
    
    # Save the minimal implementation
    with open('minimal_timemixer.py', 'w') as f:
        f.write(minimal_code)
    
    print("✅ Minimal TimeMixer saved to minimal_timemixer.py")

def test_minimal_solution():
    """Test the minimal solution."""
    print("\n🧪 TESTING MINIMAL SOLUTION")
    print("=" * 50)
    
    try:
        # Test basic imports
        import torch
        import numpy as np
        import pandas as pd
        print("✅ Basic imports work")
        
        # Test minimal TimeMixer
        from minimal_timemixer import MinimalTimeMixer
        
        # Create dummy data
        X = np.random.randn(100, 30, 1)
        train_data = {'X': X[:70]}
        test_data = {'X': X[70:]}
        
        # Create and test model
        model = MinimalTimeMixer(n_steps=30, n_pred_steps=7)
        model.fit(train_data, epochs=2)  # Quick test
        results = model.predict(test_data)
        
        print(f"✅ Minimal TimeMixer works! Prediction shape: {results['forecasting'].shape}")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Main emergency fix function."""
    print("🚨 EMERGENCY FIX FOR ANACONDA ENVIRONMENT")
    print("=" * 60)
    print("This will attempt to fix your Anaconda metadata corruption")
    print("and provide a working TimeMixer solution.")
    print()
    
    response = input("Proceed with emergency fix? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("Emergency fix cancelled.")
        return
    
    # Step 1: Fix metadata
    print("\n" + "="*60)
    print("STEP 1: FIXING ANACONDA METADATA")
    print("="*60)
    fix_anaconda_metadata()
    
    # Step 2: Minimal installation
    print("\n" + "="*60)
    print("STEP 2: MINIMAL INSTALLATION")
    print("="*60)
    minimal_pypots_install()
    
    # Step 3: Create minimal TimeMixer
    print("\n" + "="*60)
    print("STEP 3: CREATING MINIMAL TIMEMIXER")
    print("="*60)
    create_minimal_timemixer()
    
    # Step 4: Test solution
    print("\n" + "="*60)
    print("STEP 4: TESTING SOLUTION")
    print("="*60)
    success = test_minimal_solution()
    
    # Final instructions
    print("\n" + "="*60)
    print("EMERGENCY FIX COMPLETED")
    print("="*60)
    
    if success:
        print("✅ Emergency fix successful!")
        print("\nYou can now use the minimal TimeMixer:")
        print("1. Use 'from minimal_timemixer import MinimalTimeMixer'")
        print("2. Or run the updated forecasting script")
    else:
        print("❌ Emergency fix had issues.")
        print("You may need to create a new conda environment.")
    
    print("\nNext steps:")
    print("1. Restart your IDE/terminal")
    print("2. Run: python emergency_timemixer_demo.py")

if __name__ == "__main__":
    main()
'''
