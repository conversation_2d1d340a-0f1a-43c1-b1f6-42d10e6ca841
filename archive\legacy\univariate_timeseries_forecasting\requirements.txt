# Univariate Time Series Forecasting Requirements
# ===============================================

# Core dependencies for TimeMixer++ forecasting
pypots>=0.6.0
torch>=1.10.0
numpy>=1.20.0
pandas>=1.3.0

# Data visualization
matplotlib>=3.5.0
seaborn>=0.11.0

# Scientific computing
scipy>=1.7.0
scikit-learn>=1.0.0

# Data handling
h5py>=3.1.0
openpyxl>=3.0.0

# Optional: Performance optimization
numba>=0.56.0

# Optional: Interactive plotting
plotly>=5.0.0

# Development and testing (optional)
jupyter>=1.0.0
pytest>=6.0.0
