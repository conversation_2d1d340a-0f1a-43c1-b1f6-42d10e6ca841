# 🌊 TimeMixer++ Runoff Forecasting 使用指南

## 🎯 目标
使用PyPOTS库中的TimeMixer++模型对您的runoff数据进行时间序列预测。

## ❌ 当前问题
您的Anaconda环境中存在packaging包的版本冲突，导致PyPOTS无法正常导入。

## ✅ 解决方案

### 方法1: 创建新的conda环境 (推荐)

#### 步骤1: 创建环境
```bash
# 双击运行或在命令行执行
create_pypots_env.bat
```

这个脚本会：
- 创建名为`pypots`的新conda环境
- 安装Python 3.9
- 安装必要的依赖包
- 安装PyPOTS库
- 测试安装是否成功

#### 步骤2: 运行TimeMixer++
```bash
# 双击运行或在命令行执行
run_with_clean_env.bat
```

### 方法2: 手动创建环境

```bash
# 1. 创建新环境
conda create -n pypots python=3.9 -y

# 2. 激活环境
conda activate pypots

# 3. 安装依赖
conda install pandas numpy matplotlib -y
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu

# 4. 安装PyPOTS
pip install pypots

# 5. 测试安装
python -c "import pypots; print('Success!')"

# 6. 运行预测
python direct_timemixer_run.py
```

## 📊 预期输出

成功运行后，您会看到：

```
🌊 DIRECT TIMEMIXER++ RUN
========================================
🌊 Loading Runoff Data
==============================
✅ Data loaded: (24836, 2)
📅 Range: 1950-01-01 to 2017-12-31
💧 Runoff: 179.0 - 21200.0

🔧 Preparing Data (30→7)
✅ Prepared: Train=17360, Val=4960, Test=2480

🤖 Attempting TimeMixer++ Import
✅ Found TimeMixer++ (或 TimeMixer)

🚀 Training TimeMixer++
   Starting training...
   [训练进度信息]
   ✅ Training completed!
   Making predictions...
   ✅ Predictions completed: (2480, 7, 1)

📊 Creating Visualizations
✅ Saved: direct_timemixer_results.png
✅ Model saved: direct_timemixer_model.pypots

🎉 TimeMixer++ Run Completed!
```

## 📁 生成的文件

- `direct_timemixer_results.png` - 预测结果可视化
- `direct_timemixer_model.pypots` - 训练好的模型

## ⚙️ 模型配置

当前配置针对您的数据优化：

```python
配置参数:
- 输入序列: 30天
- 预测长度: 7天
- 模型维度: 128
- 层数: 2
- 训练轮数: 20 (测试用)
- 批次大小: 32
```

## 🔧 自定义配置

如需修改配置，编辑 `direct_timemixer_run.py` 中的参数：

```python
model = TimeMixer(
    n_steps=30,          # 输入天数
    n_pred_steps=7,      # 预测天数
    n_layers=2,          # 层数
    d_model=128,         # 模型维度
    epochs=20,           # 训练轮数
    batch_size=32,       # 批次大小
    # ... 其他参数
)
```

## 🐛 故障排除

### 问题1: conda命令不识别
**解决**: 确保Anaconda已正确安装并添加到PATH

### 问题2: 环境创建失败
**解决**: 
```bash
conda update conda
conda clean --all
```

### 问题3: PyPOTS安装失败
**解决**:
```bash
pip install --upgrade pip
pip install pypots --no-cache-dir
```

### 问题4: 训练时间过长
**解决**: 减少epochs数量或使用GPU

### 问题5: 内存不足
**解决**: 减小batch_size或使用更小的模型

## 📈 性能优化

### 提高准确性:
- 增加 `n_steps` (输入长度)
- 增加 `d_model` (模型复杂度)
- 增加 `epochs` (训练轮数)

### 提高速度:
- 减少 `epochs`
- 减小 `batch_size`
- 使用GPU (如果可用)

## 🎯 下一步

1. **成功运行后**: 分析预测结果，调整参数
2. **模型优化**: 根据结果调整配置参数
3. **生产部署**: 保存模型用于实际预测

## 💡 重要提示

- 新环境只需创建一次
- 每次使用前需要激活环境: `conda activate pypots`
- 模型训练可能需要几分钟时间
- 首次运行会下载必要的模型权重

---

**总结**: 由于您当前环境的包冲突问题，建议使用新的conda环境来运行TimeMixer++。这是最可靠的解决方案。
