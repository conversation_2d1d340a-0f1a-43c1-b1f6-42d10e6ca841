"""
Fix PyPOTS Installation Script
=============================

This script fixes the packaging dependency issue and properly installs PyPOTS
for TimeMixer++ usage.
"""

import subprocess
import sys
import os

def run_command(command):
    """Run a command and return success status."""
    print(f"Running: {command}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print("✅ Success")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e.stderr}")
        return False

def main():
    print("🔧 Fixing PyPOTS Installation")
    print("=" * 40)
    
    # Step 1: Fix packaging issue
    print("\n1. Fixing packaging dependency...")
    commands = [
        "pip install --upgrade pip",
        "pip install --upgrade packaging>=20.0",
        "pip install --upgrade setuptools wheel",
    ]
    
    for cmd in commands:
        if not run_command(cmd):
            print("⚠️ Warning: Command failed, continuing...")
    
    # Step 2: Install PyTorch (required for PyPOTS)
    print("\n2. Installing PyTorch...")
    torch_cmd = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
    run_command(torch_cmd)
    
    # Step 3: Install PyPOTS
    print("\n3. Installing PyPOTS...")
    pypots_commands = [
        "pip uninstall pypots -y",
        "pip install pypots --no-deps",  # Install without dependencies first
        "pip install pypots",  # Then install with dependencies
    ]
    
    for cmd in pypots_commands:
        run_command(cmd)
    
    # Step 4: Test installation
    print("\n4. Testing PyPOTS installation...")
    try:
        import pypots
        print(f"✅ PyPOTS version: {pypots.__version__}")
        
        from pypots.forecasting.timemixer import TimeMixer
        print("✅ TimeMixer imported successfully")
        
        print("\n🎉 PyPOTS installation successful!")
        print("You can now run TimeMixer++ forecasting.")
        
    except Exception as e:
        print(f"❌ Installation test failed: {e}")
        print("\n💡 Alternative solution:")
        print("Try creating a new conda environment:")
        print("conda create -n pypots python=3.9")
        print("conda activate pypots")
        print("pip install torch pandas numpy matplotlib")
        print("pip install pypots")

if __name__ == "__main__":
    main()
