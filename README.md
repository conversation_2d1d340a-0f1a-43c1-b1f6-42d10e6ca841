# TimeMixer 时间序列预测项目

基于LightGBM特征选择的TimeMixer多变量时间序列预测系统。

## 🚀 快速开始

```bash
# 安装依赖
pip install -r requirements.txt

# 启动训练 (推荐)
python start_multivariate_training.py

# 或直接运行训练
python run_my_training.py
```

## 📁 目录结构

```
TimeMixer/
├── 🎯 核心文件
│   ├── start_multivariate_training.py    # 快速启动脚本 (推荐)
│   ├── run_my_training.py                # 主训练脚本
│   ├── my_parameters.py                  # 参数配置文件
│   ├── compatible_training_runner.py     # 训练运行器
│   └── requirements.txt                  # 依赖包列表
│
├── 📊 数据文件
│   └── 1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv
│
├── 📁 子目录
│   ├── analysis/     # 分析工具和验证脚本
│   ├── docs/         # 文档和指南
│   ├── tools/        # 辅助工具和版本控制
│   ├── results/      # 训练结果和报告
│   ├── legacy_data/  # 历史数据文件
│   ├── archive/      # 归档文件
│   └── backups/      # 项目备份
```

## 🎯 核心特性

- **LightGBM特征选择**: 基于重要性分析选择5个最重要特征
- **高效训练**: 减少50%特征数量，提升训练效率
- **多种配置**: 9个专门优化的训练配置
- **完整工具链**: 数据验证、特征分析、版本控制

## 📊 特征配置

**输入特征** (5个，基于LightGBM分析):
1. **RUNOFF** (98.6%重要性) - 径流历史值
2. **VISIB** (0.2%重要性) - 能见度
3. **SLP** (0.2%重要性) - 海平面气压
4. **PRCP** (0.2%重要性) - 降水量
5. **DEWP** (0.2%重要性) - 露点温度

**输出目标**: RUNOFF未来值

## 🔧 主要配置

1. **快速验证_LightGBM5特征** - 推荐新手使用
2. **LightGBM优化_5特征精选** - 平衡性能配置
3. **LightGBM高性能_5特征** - 最高性能配置

## 📖 文档

- 查看 `docs/` 目录获取详细文档
- 推荐先阅读 `docs/LIGHTGBM_FEATURE_SELECTION_SUMMARY.md`

## 🆘 需要帮助?

1. 运行 `python start_multivariate_training.py` 获取交互式界面
2. 查看 `docs/PROJECT_GUIDE.md` 获取详细指南
3. 使用 `python tools/version_control_manager.py` 管理版本

---

**立即开始**: `python start_multivariate_training.py`
