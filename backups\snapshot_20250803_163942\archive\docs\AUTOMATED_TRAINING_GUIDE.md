# TimeMixer++ 自动化训练系统使用指南

## 🎯 系统概述

基于 `timemixer_plus_plus_example.py` 创建的自动化训练系统，能够：
- 自动创建训练脚本并在新文件夹中运行
- 使用递增数字和参数作为文件夹命名
- 自动计算并记录 MAE、RMSE、NSE、R² 等评估指标
- 将所有训练结果保存到 `timemixer_evaluation_results.csv`

## 📁 文件结构

```
C:\Users\<USER>\Desktop\timemix\
├── timemixer_plus_plus_example.py          # 原始训练模板
├── single_training_runner.py               # 单次训练运行器
├── batch_training_runner.py                # 批量训练运行器
├── quick_test_training.py                  # 快速测试脚本
├── automated_training_system.py            # 完整自动化系统
├── timemixer_evaluation_results.csv        # 训练结果记录
└── training_X_[parameters]/                # 自动生成的训练文件夹
    ├── training_X_[parameters].py          # 训练脚本
    ├── 1964-2017dailyRunoff.csv           # 数据文件副本
    └── training_X_results/                 # 模型保存目录
```

## 🚀 快速开始

### 1. 快速测试
```bash
python quick_test_training.py
```
运行一个快速的3轮训练测试，验证系统是否正常工作。

### 2. 单次训练
```bash
python single_training_runner.py
```
运行一次完整的训练，使用默认参数配置。

### 3. 批量训练
```bash
python batch_training_runner.py
```
运行7个不同配置的批量训练实验。

## 📊 评估指标说明

系统自动计算以下评估指标：

| 指标 | 英文名称 | 说明 | 理想值 |
|------|----------|------|--------|
| **MAE** | Mean Absolute Error | 平均绝对误差 | 越小越好 |
| **RMSE** | Root Mean Square Error | 均方根误差 | 越小越好 |
| **NSE** | Nash-Sutcliffe Efficiency | Nash-Sutcliffe效率系数 | 接近1最好 |
| **R²** | Coefficient of Determination | 决定系数 | 接近1最好 |

## 🔧 自定义训练

### 修改单次训练参数

编辑 `single_training_runner.py` 中的参数：

```python
parameters = {
    'n_steps': 96,           # 输入序列长度
    'n_pred_steps': 24,      # 预测序列长度
    'n_layers': 2,           # 模型层数
    'd_model': 64,           # 模型维度
    'd_ffn': 128,            # 前馈网络维度
    'epochs': 10,            # 训练轮次
    'learning_rate': 1e-3,   # 学习率
    'dropout': 0.1,          # Dropout率
    'patience': 3            # 早停耐心值
}
```

### 添加批量训练配置

编辑 `batch_training_runner.py` 中的 `parameter_sets` 列表：

```python
parameter_sets = [
    # 您的自定义配置1
    {
        'n_steps': 48,
        'n_pred_steps': 12,
        'n_layers': 1,
        'd_model': 32,
        'epochs': 5
    },
    # 您的自定义配置2
    {
        'n_steps': 96,
        'n_pred_steps': 24,
        'n_layers': 3,
        'd_model': 128,
        'epochs': 15
    }
    # ... 添加更多配置
]
```

## 📈 结果分析

### CSV文件格式

`timemixer_evaluation_results.csv` 包含以下列：

- **Training_ID**: 训练标识符 (training_1, training_2, ...)
- **Timestamp**: 训练完成时间
- **Parameters**: 训练参数字符串
- **MAE**: 平均绝对误差
- **RMSE**: 均方根误差
- **NSE**: Nash-Sutcliffe效率系数
- **R²**: 决定系数

### 查看结果

```python
import pandas as pd

# 读取结果
df = pd.read_csv('timemixer_evaluation_results.csv')

# 按MAE排序找最佳模型
best_mae = df.loc[df['MAE'].idxmin()]
print(f"最佳MAE模型: {best_mae['Training_ID']}")
print(f"MAE: {best_mae['MAE']:.4f}")

# 按R²排序找最佳模型
best_r2 = df.loc[df['R2'].idxmax()]
print(f"最佳R²模型: {best_r2['Training_ID']}")
print(f"R²: {best_r2['R2']:.4f}")
```

## 🎛️ 参数调优建议

### 模型大小参数
- **小模型**: `d_model=32, d_ffn=64, n_layers=1` (快速训练)
- **中等模型**: `d_model=64, d_ffn=128, n_layers=2` (平衡性能)
- **大模型**: `d_model=128, d_ffn=256, n_layers=3` (最佳性能)

### 序列长度参数
- **短序列**: `n_steps=24, n_pred_steps=6` (快速训练)
- **中等序列**: `n_steps=48, n_pred_steps=12` (平衡)
- **长序列**: `n_steps=96, n_pred_steps=24` (更多历史信息)

### 训练参数
- **学习率**: 通常在 `1e-4` 到 `1e-2` 之间
- **Dropout**: 通常在 `0.1` 到 `0.3` 之间
- **训练轮次**: 根据数据大小，通常 `5-50` 轮

## 📝 示例训练结果

```
Training_ID  | MAE      | RMSE     | NSE    | R²     | 参数描述
-------------|----------|----------|--------|--------|------------------
training_1   | 1158.26  | 1875.48  | 0.219  | 0.219  | 大模型,长序列
training_2   | 569.21   | 1149.38  | 0.706  | 0.706  | 小模型,短序列
```

从结果可以看出，training_2 (小模型,短序列) 在这个数据集上表现更好。

## 🔄 工作流程

1. **准备阶段**: 确保数据文件 `1964-2017dailyRunoff.csv` 存在
2. **快速测试**: 运行 `quick_test_training.py` 验证系统
3. **参数设计**: 根据需求设计不同的参数配置
4. **批量训练**: 运行 `batch_training_runner.py` 进行实验
5. **结果分析**: 查看 CSV 文件，找出最佳配置
6. **模型部署**: 使用最佳配置进行最终训练

## 🛠️ 故障排除

### 常见问题

1. **内存不足**: 减少 `d_model`、`d_ffn` 或 `n_steps`
2. **训练太慢**: 减少 `epochs` 或使用更小的模型
3. **结果不理想**: 尝试不同的学习率或增加训练轮次

### 系统要求

- Python 3.7+
- PyPOTS 1.0+
- 足够的磁盘空间存储训练结果
- 建议至少 8GB RAM

## 🎉 成功案例

系统已成功测试：
- ✅ 单次训练: training_1 (MAE: 1158.26)
- ✅ 快速测试: training_2 (MAE: 569.21, R²: 0.706)
- ✅ CSV记录: 自动保存所有结果
- ✅ 文件夹管理: 自动创建和组织

您现在可以开始使用这个自动化系统进行大规模的 TimeMixer++ 模型实验！
