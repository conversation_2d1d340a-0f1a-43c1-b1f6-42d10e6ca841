"""
单次训练运行器
==============

基于 timemixer_plus_plus_example.py 创建单次训练，
并将结果记录到 timemixer_evaluation_results.csv 中。
"""

import numpy as np
import pandas as pd
import torch
import torch.optim as optim

import sklearn.metrics as metrics
import warnings
import csv
import os
from datetime import datetime

# Try to import PyPOTS components, fall back to alternatives if not available
try:
    from pypots.optim import <PERSON> as PyPOTSAdam
    from pypots.forecasting import TimeMixer
    PYPOTS_AVAILABLE = True
except ImportError:
    print("Warning: PyPOTS TimeMixer not available. Please install a newer version of PyPOTS.")
    print("For now, using PyTorch Adam optimizer as fallback.")
    PyPOTSAdam = None
    TimeMixer = None
    PYPOTS_AVAILABLE = False

warnings.filterwarnings('ignore')

def calculate_evaluation_metrics(y_true, y_pred):
    """计算评估指标"""
    # 展平数组以便计算
    y_true_flat = y_true.flatten()
    y_pred_flat = y_pred.flatten()
    
    # MAE - 平均绝对误差
    mae = np.mean(np.abs(y_true_flat - y_pred_flat))
    
    # RMSE - 均方根误差
    rmse = np.sqrt(np.mean((y_true_flat - y_pred_flat) ** 2))
    
    # NSE - Nash-Sutcliffe效率系数
    mean_observed = np.mean(y_true_flat)
    nse = 1 - (np.sum((y_true_flat - y_pred_flat) ** 2) / 
               np.sum((y_true_flat - mean_observed) ** 2))
    
    # R² - 决定系数
    r2 = metrics.r2_score(y_true_flat, y_pred_flat)
    
    return mae, rmse, nse, r2

def save_results_to_csv(training_id, parameters, mae, rmse, nse, r2, 
                       csv_path="C:/Users/<USER>/Desktop/timemix/timemixer_evaluation_results.csv"):
    """保存结果到CSV文件"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 参数字符串
    param_items = [f"{k}={v}" for k, v in parameters.items()]
    parameters_str = "|".join(param_items)
    
    # 新行数据
    new_row = [
        training_id,
        timestamp,
        parameters_str,
        mae, rmse, nse, r2
    ]
    
    # 检查文件是否存在，如果不存在则创建
    if not os.path.exists(csv_path):
        with open(csv_path, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            writer.writerow([
                'Training_ID', 'Timestamp', 'Parameters',
                'MAE', 'RMSE', 'NSE', 'R2'
            ])
    
    # 追加新结果
    with open(csv_path, 'a', newline='', encoding='utf-8-sig') as f:
        writer = csv.writer(f)
        writer.writerow(new_row)
    
    print(f"结果已保存到: {csv_path}")

def get_next_training_id(csv_path="C:/Users/<USER>/Desktop/timemix/timemixer_evaluation_results.csv"):
    """获取下一个训练ID"""
    if os.path.exists(csv_path):
        try:
            df = pd.read_csv(csv_path)
            if not df.empty and 'Training_ID' in df.columns:
                # 提取数字部分并找到最大值
                max_id = 0
                for training_id in df['Training_ID']:
                    if isinstance(training_id, str) and training_id.startswith('training_'):
                        try:
                            num = int(training_id.split('_')[1])
                            max_id = max(max_id, num)
                        except:
                            continue
                return f"training_{max_id + 1}"
        except:
            pass
    return "training_1"

def run_timemixer_training(parameters):
    """运行TimeMixer训练"""

    # 检查PyPOTS是否可用
    if not PYPOTS_AVAILABLE:
        print("❌ PyPOTS TimeMixer不可用。请安装包含TimeMixer的PyPOTS版本。")
        print("尝试运行: pip install git+https://github.com/WenjieDu/PyPOTS.git")
        return None

    # 生成训练ID
    training_id = get_next_training_id()
    print(f"训练ID: {training_id}")
    print(f"参数: {parameters}")

    # 检查设备设置
    if 'device' in parameters:
        if parameters['device'] == 'cuda' and not torch.cuda.is_available():
            print("⚠ 请求使用GPU但CUDA不可用，切换到CPU")
            device = 'cuda'
        else:
            device = parameters['device']
    else:
        device = 'cuda' if torch.cuda.is_available() else 'cpu'

    print(f"使用设备: {device}")
    if device == 'cuda':
        print(f"GPU: {torch.cuda.get_device_name(0)}")

    # --- 数据加载和预处理 (基于原始模板) ---
    your_data_path = '1964-2017dailyRunoff.csv'
    date_column_name = 'DATA'
    target_column_name = 'runoff'

    # 定义模型所需的输入和预测步长
    N_STEPS_PER_SAMPLE = parameters.get('n_steps', 96)
    N_PRED_STEPS = parameters.get('n_pred_steps', 24)
    
    try:
        # 读取CSV文件
        df = pd.read_csv(your_data_path)
        df[date_column_name] = pd.to_datetime(df[date_column_name], format='%Y/%m/%d')
        df = df.set_index(date_column_name)
        
        # 检查数据并处理缺失值
        full_time_series_raw = df[target_column_name].values.astype(np.float32)
        
        print(f"原始数据总长度: {len(full_time_series_raw)}")
        print(f"原始数据缺失值数量: {np.isnan(full_time_series_raw).sum()}")
        
        # 处理缺失值
        if np.isnan(full_time_series_raw).any():
            print("警告：数据中存在缺失值，将使用均值填充。")
            mean_value = np.nanmean(full_time_series_raw)
            full_time_series = np.nan_to_num(full_time_series_raw, nan=mean_value)
        else:
            full_time_series = full_time_series_raw
        
        # 将一维时间序列转换为三维格式
        total_sample_len = N_STEPS_PER_SAMPLE + N_PRED_STEPS
        n_samples = (len(full_time_series) - total_sample_len) + 1
        
        if n_samples <= 0:
            raise ValueError(f"数据集太短，无法创建足够长度为 {total_sample_len} 的样本。")
        
        your_dataset_X = np.zeros((n_samples, total_sample_len, 1), dtype=np.float32)
        
        for i in range(n_samples):
            your_dataset_X[i, :, 0] = full_time_series[i : i + total_sample_len]
        
        print(f"处理后用于模型的数据形状: {your_dataset_X.shape}")
        
    except Exception as e:
        print(f"加载或处理数据集时发生错误：{e}")
        return None
    
    # --- 划分数据集 ---
    def split_data_for_forecasting(data_X, n_steps, n_pred_steps, train_ratio=0.7, val_ratio=0.15):
        n_total_samples = data_X.shape[0]
        train_end = int(n_total_samples * train_ratio)
        val_end = int(n_total_samples * (train_ratio + val_ratio))
        
        X_input = data_X[:, :n_steps, :]
        X_pred = data_X[:, n_steps:, :]
        
        train_data = {'X': X_input[:train_end], 'X_pred': X_pred[:train_end]}
        val_data = {'X': X_input[train_end:val_end], 'X_pred': X_pred[train_end:val_end]}
        test_data = {'X': X_input[val_end:], 'X_pred': X_pred[val_end:]}
        
        return train_data, val_data, test_data
    
    dataset_for_FORE_training, dataset_for_FORE_validating, dataset_for_FORE_testing = \
        split_data_for_forecasting(your_dataset_X, N_STEPS_PER_SAMPLE, N_PRED_STEPS)
    
    print(f"\n数据集划分结果:")
    print(f"  训练集形状: {dataset_for_FORE_training['X'].shape}")
    print(f"  验证集形状: {dataset_for_FORE_validating['X'].shape}")
    print(f"  测试集形状: {dataset_for_FORE_testing['X'].shape}")
    
    # --- 创建和训练模型 ---
    # 获取所有可能的参数，提供默认值
    model_params = {
        'n_steps': N_STEPS_PER_SAMPLE,
        'n_features': 1,
        'n_pred_steps': N_PRED_STEPS,
        'n_pred_features': 1,
        'term': parameters.get('term', "short"),
        'n_layers': parameters.get('n_layers', 2),
        'top_k': parameters.get('top_k', 5),
        'd_model': parameters.get('d_model', 32),
        'd_ffn': parameters.get('d_ffn', parameters.get('d_model', 32) * 2),  # 默认为d_model的2倍
        'moving_avg': parameters.get('moving_avg', 25),
        'downsampling_window': parameters.get('downsampling_window', 2),
        'downsampling_layers': parameters.get('downsampling_layers', 1),
        'use_norm': parameters.get('use_norm', True),
        'dropout': parameters.get('dropout', 0.1),
        'batch_size': parameters.get('batch_size', 32),
        'epochs': parameters.get('epochs', 10),
        'patience': parameters.get('patience', 3),
        'optimizer': PyPOTSAdam(lr=parameters.get('learning_rate', 1e-3)),
        'num_workers': parameters.get('num_workers', 0),
        'device': device,
        'saving_path': f"{training_id}_results",
        'model_saving_strategy': parameters.get('model_saving_strategy', "best"),
    }

    # 创建模型
    timemixer = TimeMixer(**model_params)
    
    print(f"\nTimeMixer++ 模型初始化完成，参数如下:")
    print(f"  输入步长 (n_steps): {timemixer.n_steps}")
    print(f"  特征数量 (n_features): {timemixer.n_features}")
    print(f"  预测步长 (n_pred_steps): {timemixer.n_pred_steps}")
    print(f"  训练轮次 (epochs): {timemixer.epochs}")
    print(f"  模型将保存到: {timemixer.saving_path}")
    
    # 训练模型
    print("\n开始训练 TimeMixer++ 模型...")
    timemixer.fit(train_set=dataset_for_FORE_training, val_set=dataset_for_FORE_validating)
    print("模型训练完成。")
    
    # 进行预测
    print("\n开始在测试集上进行预测...")
    timemixer_results = timemixer.predict(dataset_for_FORE_testing)
    timemixer_prediction = timemixer_results["forecasting"]
    print(f"预测结果形状: {timemixer_prediction.shape}")
    
    # 计算评估指标
    true_values_for_testing = dataset_for_FORE_testing['X_pred']
    mae, rmse, nse, r2 = calculate_evaluation_metrics(true_values_for_testing, timemixer_prediction)
    
    print(f"\n=== 详细评估结果 ===")
    print(f"MAE (平均绝对误差): {mae:.6f}")
    print(f"RMSE (均方根误差): {rmse:.6f}")
    print(f"NSE (Nash-Sutcliffe效率系数): {nse:.6f}")
    print(f"R² (决定系数): {r2:.6f}")
    
    # 保存结果到CSV
    save_results_to_csv(training_id, parameters, mae, rmse, nse, r2)
    
    print(f"\n--- 训练 {training_id} 完成 ---")
    
    return {
        'training_id': training_id,
        'mae': mae,
        'rmse': rmse,
        'nse': nse,
        'r2': r2
    }

def main():
    """主函数 - 运行单次训练"""
    
    # 定义训练参数 (您可以修改这些参数)
    parameters = {
        'n_steps': 96,
        'n_pred_steps': 24,
        'n_layers': 2,
        'top_k': 5,
        'd_model': 64,
        'd_ffn': 128,
        'moving_avg': 25,
        'dropout': 0.1,
        'epochs': 10,
        'learning_rate': 1e-3,
        'patience': 3
    }
    
    print("TimeMixer++ 单次训练运行器")
    print("="*50)
    
    # 运行训练
    result = run_timemixer_training(parameters)
    
    if result:
        print(f"\n训练成功完成！")
        print(f"训练ID: {result['training_id']}")
        print(f"最终结果:")
        print(f"  MAE: {result['mae']:.6f}")
        print(f"  RMSE: {result['rmse']:.6f}")
        print(f"  NSE: {result['nse']:.6f}")
        print(f"  R²: {result['r2']:.6f}")
    else:
        print("训练失败！")

if __name__ == "__main__":
    main()
