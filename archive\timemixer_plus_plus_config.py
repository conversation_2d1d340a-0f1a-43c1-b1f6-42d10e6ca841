"""
TimeMixer++ Model Configuration for PyPOTS
===========================================

This module provides configuration classes and utilities for the TimeMixer++ model
from the PyPOTS library. TimeMixer++ is an advanced time series forecasting and 
imputation model that builds upon the original TimeMixer architecture.

Based on PyPOTS library: https://github.com/WenjieDu/PyPOTS
"""

import numpy as np
from typing import Optional, Union, Dict, Any
from dataclasses import dataclass


@dataclass
class TimeMixerPPConfig:
    """
    Configuration class for TimeMixer++ model parameters.
    
    This class contains all the necessary parameters to configure a TimeMixer++
    model for both forecasting and imputation tasks.
    """
    
    # Data dimensions
    n_steps: int = 96  # Number of time steps in input sequence
    n_features: int = 7  # Number of features in the time series
    n_pred_steps: Optional[int] = None  # Number of prediction steps (for forecasting)
    n_pred_features: Optional[int] = None  # Number of prediction features (for forecasting)
    
    # Model architecture parameters
    n_layers: int = 2  # Number of layers in the model
    d_model: int = 256  # Model dimension
    d_ffn: int = 512  # Feed-forward network dimension
    top_k: int = 5  # Top-k parameter for TimeMixer
    dropout: float = 0.1  # Dropout rate
    
    # Training parameters
    batch_size: int = 32  # Batch size for training
    epochs: int = 100  # Number of training epochs
    patience: Optional[int] = 10  # Early stopping patience
    
    # Loss and optimization
    learning_rate: float = 0.001  # Learning rate for optimizer
    weight_decay: float = 1e-4  # Weight decay for regularization
    
    # Device and performance
    device: Optional[str] = None  # Device to run on ('cuda', 'cpu', or None for auto)
    num_workers: int = 0  # Number of data loading workers
    
    # Model saving
    saving_path: Optional[str] = None  # Path to save model checkpoints
    model_saving_strategy: str = 'best'  # Strategy for saving models
    verbose: bool = True  # Whether to print training logs
    
    def __post_init__(self):
        """Validate configuration parameters after initialization."""
        if self.n_steps <= 0:
            raise ValueError("n_steps must be positive")
        if self.n_features <= 0:
            raise ValueError("n_features must be positive")
        if self.d_model <= 0:
            raise ValueError("d_model must be positive")
        if not 0 <= self.dropout <= 1:
            raise ValueError("dropout must be between 0 and 1")
        if self.model_saving_strategy not in ['best', 'better', 'all', None]:
            raise ValueError("model_saving_strategy must be one of ['best', 'better', 'all', None]")


class TimeMixerPPModelFactory:
    """
    Factory class for creating TimeMixer++ models with different configurations.
    """
    
    @staticmethod
    def create_imputation_model(config: TimeMixerPPConfig):
        """
        Create a TimeMixer++ model for imputation tasks.
        
        Args:
            config: TimeMixerPPConfig object with model parameters
            
        Returns:
            Configured TimeMixer++ model for imputation
        """
        try:
            from pypots.imputation.timemixerpp import TimeMixerPP
        except ImportError:
            raise ImportError(
                "PyPOTS library not found. Please install it using: pip install pypots"
            )
        
        model = TimeMixerPP(
            n_steps=config.n_steps,
            n_features=config.n_features,
            n_layers=config.n_layers,
            d_model=config.d_model,
            d_ffn=config.d_ffn,
            top_k=config.top_k,
            dropout=config.dropout,
            batch_size=config.batch_size,
            epochs=config.epochs,
            patience=config.patience,
            num_workers=config.num_workers,
            device=config.device,
            saving_path=config.saving_path,
            model_saving_strategy=config.model_saving_strategy,
            verbose=config.verbose
        )
        
        return model
    
    @staticmethod
    def create_forecasting_model(config: TimeMixerPPConfig):
        """
        Create a TimeMixer++ model for forecasting tasks.
        
        Args:
            config: TimeMixerPPConfig object with model parameters
            
        Returns:
            Configured TimeMixer++ model for forecasting
        """
        if config.n_pred_steps is None:
            raise ValueError("n_pred_steps must be specified for forecasting tasks")
        
        try:
            from pypots.forecasting.timemixerpp import TimeMixerPP
        except ImportError:
            # If TimeMixer++ is not available in forecasting, try TimeMixer
            try:
                from pypots.forecasting.timemixer import TimeMixer
                print("Warning: TimeMixer++ not found in forecasting module, using TimeMixer instead")
                
                model = TimeMixer(
                    n_steps=config.n_steps,
                    n_features=config.n_features,
                    n_pred_steps=config.n_pred_steps,
                    n_pred_features=config.n_pred_features or config.n_features,
                    n_layers=config.n_layers,
                    d_model=config.d_model,
                    d_ffn=config.d_ffn,
                    top_k=config.top_k,
                    dropout=config.dropout,
                    batch_size=config.batch_size,
                    epochs=config.epochs,
                    patience=config.patience,
                    num_workers=config.num_workers,
                    device=config.device,
                    saving_path=config.saving_path,
                    model_saving_strategy=config.model_saving_strategy,
                    verbose=config.verbose
                )
                return model
            except ImportError:
                raise ImportError(
                    "PyPOTS library not found or TimeMixer models not available. "
                    "Please install/update PyPOTS: pip install pypots"
                )


def get_default_configs() -> Dict[str, TimeMixerPPConfig]:
    """
    Get a dictionary of default configurations for different use cases.
    
    Returns:
        Dictionary containing default configurations
    """
    configs = {
        'small': TimeMixerPPConfig(
            n_steps=48,
            n_features=7,
            n_layers=1,
            d_model=128,
            d_ffn=256,
            top_k=3,
            dropout=0.1,
            batch_size=64,
            epochs=50
        ),
        
        'medium': TimeMixerPPConfig(
            n_steps=96,
            n_features=7,
            n_layers=2,
            d_model=256,
            d_ffn=512,
            top_k=5,
            dropout=0.1,
            batch_size=32,
            epochs=100
        ),
        
        'large': TimeMixerPPConfig(
            n_steps=192,
            n_features=21,
            n_layers=3,
            d_model=512,
            d_ffn=1024,
            top_k=7,
            dropout=0.2,
            batch_size=16,
            epochs=150
        ),
        
        'forecasting': TimeMixerPPConfig(
            n_steps=96,
            n_features=7,
            n_pred_steps=24,
            n_pred_features=7,
            n_layers=2,
            d_model=256,
            d_ffn=512,
            top_k=5,
            dropout=0.1,
            batch_size=32,
            epochs=100
        )
    }
    
    return configs


def create_custom_config(**kwargs) -> TimeMixerPPConfig:
    """
    Create a custom TimeMixer++ configuration.
    
    Args:
        **kwargs: Configuration parameters to override defaults
        
    Returns:
        Custom TimeMixerPPConfig object
    """
    default_config = TimeMixerPPConfig()
    
    # Update with custom parameters
    for key, value in kwargs.items():
        if hasattr(default_config, key):
            setattr(default_config, key, value)
        else:
            raise ValueError(f"Unknown configuration parameter: {key}")
    
    return default_config


# Example usage and utility functions
def example_usage():
    """
    Example of how to use the TimeMixer++ configuration.
    """
    print("TimeMixer++ Configuration Examples")
    print("=" * 40)
    
    # Get default configurations
    configs = get_default_configs()
    
    # Example 1: Using a predefined configuration
    print("\n1. Using predefined 'medium' configuration:")
    medium_config = configs['medium']
    print(f"   n_steps: {medium_config.n_steps}")
    print(f"   n_features: {medium_config.n_features}")
    print(f"   d_model: {medium_config.d_model}")
    
    # Example 2: Creating a custom configuration
    print("\n2. Creating custom configuration:")
    custom_config = create_custom_config(
        n_steps=120,
        n_features=10,
        d_model=384,
        epochs=200,
        learning_rate=0.0005
    )
    print(f"   n_steps: {custom_config.n_steps}")
    print(f"   n_features: {custom_config.n_features}")
    print(f"   d_model: {custom_config.d_model}")
    print(f"   epochs: {custom_config.epochs}")
    
    # Example 3: Creating models (commented out as it requires PyPOTS)
    print("\n3. Model creation example (requires PyPOTS installation):")
    print("   # For imputation:")
    print("   # model = TimeMixerPPModelFactory.create_imputation_model(medium_config)")
    print("   # For forecasting:")
    print("   # forecasting_config = configs['forecasting']")
    print("   # model = TimeMixerPPModelFactory.create_forecasting_model(forecasting_config)")


if __name__ == "__main__":
    example_usage()
