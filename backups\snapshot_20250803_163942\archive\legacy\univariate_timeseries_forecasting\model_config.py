"""
Univariate TimeMixer++ Model Configuration
==========================================

This module provides specialized configuration for univariate time series
forecasting using TimeMixer++ from PyPOTS.
"""

import numpy as np
from typing import Optional, Dict, Any
from dataclasses import dataclass


@dataclass
class UnivariateTimeMixerConfig:
    """
    Configuration class specifically for univariate time series forecasting.
    
    This class is optimized for single-variable input and output scenarios.
    """
    
    # Data dimensions (fixed for univariate)
    n_features: int = 1  # Always 1 for univariate
    n_pred_features: int = 1  # Always 1 for univariate output
    
    # Sequence parameters
    n_steps: int = 30  # Input sequence length (lookback window)
    n_pred_steps: int = 7  # Prediction horizon (forecast steps)
    
    # Model architecture parameters
    n_layers: int = 2  # Number of TimeMixer layers
    d_model: int = 128  # Model dimension (reduced for univariate)
    d_ffn: int = 256  # Feed-forward network dimension
    top_k: int = 3  # Top-k parameter for TimeMixer (reduced for univariate)
    dropout: float = 0.1  # Dropout rate
    
    # Training parameters
    batch_size: int = 32  # Batch size for training
    epochs: int = 100  # Number of training epochs
    patience: int = 15  # Early stopping patience
    
    # Optimization parameters
    learning_rate: float = 0.001  # Learning rate
    weight_decay: float = 1e-4  # Weight decay for regularization
    
    # Device and performance
    device: Optional[str] = None  # Device ('cuda', 'cpu', or None for auto)
    num_workers: int = 0  # Number of data loading workers
    
    # Model saving and logging
    saving_path: Optional[str] = None  # Path to save model checkpoints
    model_saving_strategy: str = 'best'  # Model saving strategy
    verbose: bool = True  # Whether to print training logs
    
    def __post_init__(self):
        """Validate configuration parameters after initialization."""
        # Fixed validations for univariate case
        if self.n_features != 1:
            raise ValueError("n_features must be 1 for univariate forecasting")
        if self.n_pred_features != 1:
            raise ValueError("n_pred_features must be 1 for univariate forecasting")
        
        # General validations
        if self.n_steps <= 0:
            raise ValueError("n_steps must be positive")
        if self.n_pred_steps <= 0:
            raise ValueError("n_pred_steps must be positive")
        if self.d_model <= 0:
            raise ValueError("d_model must be positive")
        if not 0 <= self.dropout <= 1:
            raise ValueError("dropout must be between 0 and 1")
        if self.model_saving_strategy not in ['best', 'better', 'all', None]:
            raise ValueError("model_saving_strategy must be one of ['best', 'better', 'all', None]")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            'n_steps': self.n_steps,
            'n_features': self.n_features,
            'n_pred_steps': self.n_pred_steps,
            'n_pred_features': self.n_pred_features,
            'n_layers': self.n_layers,
            'd_model': self.d_model,
            'd_ffn': self.d_ffn,
            'top_k': self.top_k,
            'dropout': self.dropout,
            'batch_size': self.batch_size,
            'epochs': self.epochs,
            'patience': self.patience,
            'num_workers': self.num_workers,
            'device': self.device,
            'saving_path': self.saving_path,
            'model_saving_strategy': self.model_saving_strategy,
            'verbose': self.verbose
        }


class UnivariateModelFactory:
    """
    Factory class for creating univariate TimeMixer++ models.
    """
    
    @staticmethod
    def create_forecasting_model(config: UnivariateTimeMixerConfig):
        """
        Create a TimeMixer++ model for univariate forecasting.
        
        Args:
            config: UnivariateTimeMixerConfig object
            
        Returns:
            Configured TimeMixer++ model for forecasting
        """
        try:
            # Try TimeMixer++ first
            from pypots.forecasting.timemixerpp import TimeMixerPP
            print("✓ Using TimeMixer++ for forecasting")
            
            model = TimeMixerPP(
                n_steps=config.n_steps,
                n_features=config.n_features,
                n_pred_steps=config.n_pred_steps,
                n_pred_features=config.n_pred_features,
                n_layers=config.n_layers,
                d_model=config.d_model,
                d_ffn=config.d_ffn,
                top_k=config.top_k,
                dropout=config.dropout,
                batch_size=config.batch_size,
                epochs=config.epochs,
                patience=config.patience,
                num_workers=config.num_workers,
                device=config.device,
                saving_path=config.saving_path,
                model_saving_strategy=config.model_saving_strategy,
                verbose=config.verbose
            )
            
        except ImportError:
            # Fallback to TimeMixer
            try:
                from pypots.forecasting.timemixer import TimeMixer
                print("⚠ TimeMixer++ not available, using TimeMixer instead")
                
                model = TimeMixer(
                    n_steps=config.n_steps,
                    n_features=config.n_features,
                    n_pred_steps=config.n_pred_steps,
                    n_pred_features=config.n_pred_features,
                    n_layers=config.n_layers,
                    d_model=config.d_model,
                    d_ffn=config.d_ffn,
                    top_k=config.top_k,
                    dropout=config.dropout,
                    batch_size=config.batch_size,
                    epochs=config.epochs,
                    patience=config.patience,
                    num_workers=config.num_workers,
                    device=config.device,
                    saving_path=config.saving_path,
                    model_saving_strategy=config.model_saving_strategy,
                    verbose=config.verbose
                )
                
            except ImportError:
                raise ImportError(
                    "Neither TimeMixer++ nor TimeMixer is available. "
                    "Please install/update PyPOTS: pip install pypots"
                )
        
        return model


def get_univariate_configs() -> Dict[str, UnivariateTimeMixerConfig]:
    """
    Get predefined configurations for different univariate forecasting scenarios.
    
    Returns:
        Dictionary containing predefined configurations
    """
    configs = {
        'short_term': UnivariateTimeMixerConfig(
            n_steps=14,          # 2 weeks lookback
            n_pred_steps=3,      # 3 days forecast
            d_model=64,          # Small model
            n_layers=1,
            top_k=2,
            epochs=50,
            batch_size=64
        ),
        
        'medium_term': UnivariateTimeMixerConfig(
            n_steps=30,          # 1 month lookback
            n_pred_steps=7,      # 1 week forecast
            d_model=128,         # Medium model
            n_layers=2,
            top_k=3,
            epochs=100,
            batch_size=32
        ),
        
        'long_term': UnivariateTimeMixerConfig(
            n_steps=90,          # 3 months lookback
            n_pred_steps=30,     # 1 month forecast
            d_model=256,         # Large model
            n_layers=3,
            top_k=5,
            epochs=150,
            batch_size=16
        ),
        
        'daily_to_weekly': UnivariateTimeMixerConfig(
            n_steps=30,          # 30 days lookback
            n_pred_steps=7,      # 7 days forecast
            d_model=128,
            n_layers=2,
            top_k=3,
            epochs=100,
            batch_size=32
        ),
        
        'quick_test': UnivariateTimeMixerConfig(
            n_steps=10,          # Short lookback for testing
            n_pred_steps=3,      # Short forecast for testing
            d_model=64,          # Small model for speed
            n_layers=1,
            top_k=2,
            epochs=10,           # Few epochs for quick testing
            batch_size=64
        )
    }
    
    return configs


def create_custom_univariate_config(**kwargs) -> UnivariateTimeMixerConfig:
    """
    Create a custom univariate forecasting configuration.
    
    Args:
        **kwargs: Configuration parameters to override defaults
        
    Returns:
        Custom UnivariateTimeMixerConfig object
    """
    # Start with medium_term as default
    default_config = get_univariate_configs()['medium_term']
    
    # Create new config with updated parameters
    config_dict = default_config.to_dict()
    
    # Update with custom parameters
    for key, value in kwargs.items():
        if key in config_dict:
            config_dict[key] = value
        else:
            raise ValueError(f"Unknown configuration parameter: {key}")
    
    # Remove the dict-specific parameters and create new config
    config_params = {k: v for k, v in config_dict.items() 
                    if k not in ['n_features', 'n_pred_features']}  # These are fixed for univariate
    
    return UnivariateTimeMixerConfig(**config_params)


def print_config_summary(config: UnivariateTimeMixerConfig, config_name: str = "Custom"):
    """
    Print a summary of the configuration.
    
    Args:
        config: Configuration object
        config_name: Name of the configuration
    """
    print(f"\n{config_name} Configuration Summary:")
    print("=" * 40)
    print(f"Data Configuration:")
    print(f"  - Input sequence length: {config.n_steps}")
    print(f"  - Prediction horizon: {config.n_pred_steps}")
    print(f"  - Features: {config.n_features} (univariate)")
    
    print(f"\nModel Architecture:")
    print(f"  - Model dimension: {config.d_model}")
    print(f"  - Number of layers: {config.n_layers}")
    print(f"  - FFN dimension: {config.d_ffn}")
    print(f"  - Top-k parameter: {config.top_k}")
    print(f"  - Dropout rate: {config.dropout}")
    
    print(f"\nTraining Configuration:")
    print(f"  - Batch size: {config.batch_size}")
    print(f"  - Epochs: {config.epochs}")
    print(f"  - Patience: {config.patience}")
    print(f"  - Learning rate: {config.learning_rate}")


def example_usage():
    """
    Example of how to use the univariate configuration.
    """
    print("Univariate TimeMixer++ Configuration Examples")
    print("=" * 50)
    
    # Get predefined configurations
    configs = get_univariate_configs()
    
    # Show available configurations
    print(f"\nAvailable configurations: {list(configs.keys())}")
    
    # Example 1: Using predefined configuration
    print_config_summary(configs['medium_term'], "Medium Term")
    
    # Example 2: Creating custom configuration
    custom_config = create_custom_univariate_config(
        n_steps=45,
        n_pred_steps=14,
        d_model=192,
        epochs=80
    )
    print_config_summary(custom_config, "Custom")
    
    # Example 3: Model creation (commented out as it requires PyPOTS)
    print(f"\nModel Creation Example:")
    print(f"# model = UnivariateModelFactory.create_forecasting_model(custom_config)")


if __name__ == "__main__":
    example_usage()
