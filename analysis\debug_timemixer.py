"""
调试TimeMixer维度问题
====================

深入分析TimeMixer的维度不匹配问题
"""

import torch
import numpy as np

def analyze_moving_average_issue():
    """分析移动平均维度问题"""
    
    print("🔍 分析TimeMixer移动平均维度问题")
    print("="*60)
    
    # 模拟TimeMixer中的移动平均计算
    n_steps = 96
    moving_avg = 12
    batch_size = 8
    d_model = 64
    
    print(f"输入参数:")
    print(f"  n_steps: {n_steps}")
    print(f"  moving_avg: {moving_avg}")
    print(f"  batch_size: {batch_size}")
    print(f"  d_model: {d_model}")
    
    # 创建模拟输入张量
    x = torch.randn(batch_size, n_steps, d_model)
    print(f"\n输入张量形状: {x.shape}")
    
    # 模拟TimeMixer中的移动平均计算
    # 这是导致问题的关键部分
    try:
        # 方法1: 使用unfold进行移动平均
        print(f"\n方法1: unfold移动平均")
        unfolded = x.unfold(dimension=1, size=moving_avg, step=1)
        print(f"unfold后形状: {unfolded.shape}")
        moving_mean = unfolded.mean(dim=-1)
        print(f"移动平均形状: {moving_mean.shape}")
        
        # 检查维度匹配
        if x.shape[1] != moving_mean.shape[1]:
            print(f"❌ 维度不匹配: 输入{x.shape[1]} vs 移动平均{moving_mean.shape[1]}")
            print(f"差异: {x.shape[1] - moving_mean.shape[1]}")
        else:
            print(f"✅ 维度匹配")
            
    except Exception as e:
        print(f"❌ 方法1失败: {e}")
    
    try:
        # 方法2: 使用conv1d进行移动平均
        print(f"\n方法2: conv1d移动平均")
        # 创建移动平均卷积核
        kernel = torch.ones(1, 1, moving_avg) / moving_avg
        
        # 对每个特征维度分别进行卷积
        x_reshaped = x.transpose(1, 2)  # (batch, d_model, n_steps)
        print(f"转置后形状: {x_reshaped.shape}")
        
        # 使用padding='same'保持长度不变
        conv_result = torch.nn.functional.conv1d(
            x_reshaped, 
            kernel.repeat(d_model, 1, 1), 
            groups=d_model,
            padding='same'
        )
        print(f"卷积结果形状: {conv_result.shape}")
        
        moving_mean_conv = conv_result.transpose(1, 2)  # 转回 (batch, n_steps, d_model)
        print(f"最终移动平均形状: {moving_mean_conv.shape}")
        
        if x.shape == moving_mean_conv.shape:
            print(f"✅ 方法2维度匹配")
        else:
            print(f"❌ 方法2维度不匹配")
            
    except Exception as e:
        print(f"❌ 方法2失败: {e}")

def test_different_parameters():
    """测试不同参数组合"""
    
    print(f"\n🧪 测试不同参数组合")
    print("="*60)
    
    test_cases = [
        {'n_steps': 96, 'moving_avg': 12},
        {'n_steps': 96, 'moving_avg': 24},
        {'n_steps': 96, 'moving_avg': 48},
        {'n_steps': 72, 'moving_avg': 24},
        {'n_steps': 90, 'moving_avg': 12},
    ]
    
    for case in test_cases:
        n_steps = case['n_steps']
        moving_avg = case['moving_avg']
        
        print(f"\n测试: n_steps={n_steps}, moving_avg={moving_avg}")
        
        # 创建测试张量
        x = torch.randn(1, n_steps, 1)
        
        try:
            # 使用unfold
            unfolded = x.unfold(dimension=1, size=moving_avg, step=1)
            moving_mean = unfolded.mean(dim=-1)
            
            print(f"  输入形状: {x.shape}")
            print(f"  移动平均形状: {moving_mean.shape}")
            print(f"  长度差异: {x.shape[1] - moving_mean.shape[1]}")
            
            if x.shape[1] == moving_mean.shape[1]:
                print(f"  ✅ 维度匹配")
            else:
                print(f"  ❌ 维度不匹配")
                
        except Exception as e:
            print(f"  ❌ 错误: {e}")

def suggest_solution():
    """建议解决方案"""
    
    print(f"\n💡 解决方案建议")
    print("="*60)
    
    print("问题分析:")
    print("- TimeMixer使用unfold进行移动平均计算")
    print("- unfold(size=moving_avg, step=1)会减少序列长度")
    print("- 输出长度 = 输入长度 - moving_avg + 1")
    print("- 例如: 输入96, moving_avg=12 → 输出85")
    
    print(f"\n解决方案:")
    print("1. 修改TimeMixer源码使用padding")
    print("2. 调整输入序列长度")
    print("3. 使用不同的moving_avg值")
    print("4. 使用自定义的TimeMixer实现")
    
    print(f"\n推荐方案:")
    print("- 使用较小的moving_avg值 (如5-10)")
    print("- 或者修改compatible_training_runner.py使用简化模型")

if __name__ == "__main__":
    analyze_moving_average_issue()
    test_different_parameters()
    suggest_solution()
