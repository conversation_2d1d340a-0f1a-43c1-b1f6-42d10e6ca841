# TimeMixer项目版本控制指南

## 🎯 概述

为您的TimeMixer深度学习项目建立了完整的代码保存和回溯机制，确保代码安全和版本可追溯性。

## 📁 版本控制文件说明

### 核心工具
- `version_control_manager.py` - **主控制器** (推荐使用)
- `backup_project.py` - 项目备份工具
- `init_git_version_control.py` - Git版本控制初始化
- `track_code_changes.py` - 代码变更追踪
- `quick_restore.py` - 快速恢复工具

### 生成的目录
- `backups/` - 项目备份存储目录
- `.code_tracking/` - 代码变更追踪数据
- `.git/` - Git版本控制数据 (如果启用)

## 🚀 快速开始

### 1. 一键设置 (推荐)
```bash
python version_control_manager.py
```
选择 "1. 快速设置版本控制"，系统将自动：
- 创建初始项目备份
- 初始化Git仓库 (如果Git可用)
- 启用代码变更追踪

### 2. 手动设置
如果需要分步设置：

```bash
# 创建项目备份
python backup_project.py

# 初始化Git版本控制
python init_git_version_control.py

# 启用代码变更追踪
python track_code_changes.py
```

## 📦 备份功能

### 自动备份
- **核心代码文件**: 所有Python训练脚本
- **配置文件**: 参数配置、项目文档
- **数据文件**: CSV数据文件、结果文件
- **归档文件**: archive目录中的重要文件

### 备份命名
- 格式: `snapshot_YYYYMMDD_HHMMSS`
- 包含: 时间戳、文件数量、MD5校验

### 使用方法
```bash
# 创建备份
python backup_project.py

# 或使用主控制器
python version_control_manager.py
# 选择 "2. 创建项目备份"
```

## 🌿 Git版本控制

### 自动配置
- 创建适合深度学习项目的 `.gitignore`
- 初始化仓库并创建基线提交
- 创建 `development` 分支用于开发

### 忽略文件类型
- Python缓存文件 (`__pycache__`, `*.pyc`)
- 模型权重文件 (`*.pth`, `*.pt`)
- 训练日志 (`logs/`, `runs/`)
- 临时文件 (`*.tmp`, `*.log`)

### 使用建议
```bash
# 查看状态
git status

# 添加文件
git add .

# 创建提交
git commit -m "描述您的更改"

# 切换到开发分支
git checkout development
```

## 🔍 代码变更追踪

### 监控文件
- `compatible_training_runner.py`
- `my_parameters.py`
- `run_my_training.py`
- `run_advanced_training.py`
- `debug_timemixer.py`
- `test_normalization.py`
- `validate_parameters.py`

### 功能特性
- **自动检测**: 文件内容变更检测
- **差异生成**: 详细的代码差异对比
- **快照保存**: 每次变更的文件快照
- **变更日志**: 完整的变更历史记录

### 使用方法
```bash
# 扫描变更
python track_code_changes.py
# 选择 "1. 扫描文件变更"

# 查看历史
python track_code_changes.py
# 选择 "2. 查看变更历史"
```

## 🔄 快速恢复

### 恢复选项
1. **项目备份**: 完整的项目状态恢复
2. **Git提交**: 恢复到任意Git提交
3. **代码快照**: 恢复单个文件的历史版本

### 安全机制
- **临时备份**: 恢复前自动备份当前状态
- **确认提示**: 防止意外恢复
- **增量恢复**: 可选择恢复部分文件

### 使用方法
```bash
# 交互式恢复
python quick_restore.py

# 或使用主控制器
python version_control_manager.py
# 选择 "4. 快速恢复"
```

## 🔧 日常使用工作流

### 开始实验前
```bash
# 1. 创建备份
python version_control_manager.py
# 选择 "6. 紧急备份"

# 2. 或者创建Git分支
git checkout -b experiment_new_feature
```

### 实验过程中
```bash
# 定期检查变更
python version_control_manager.py
# 选择 "3. 扫描代码变更"

# 重要节点创建备份
python version_control_manager.py
# 选择 "2. 创建项目备份"
```

### 实验完成后
```bash
# 日常维护
python version_control_manager.py
# 选择 "5. 日常维护"
# 系统会自动扫描变更、创建备份、提交Git
```

## 🆘 紧急情况处理

### 代码出错需要回滚
```bash
# 1. 快速恢复
python quick_restore.py

# 2. 选择最近的备份或Git提交
# 3. 系统会自动备份当前状态后恢复
```

### 意外删除文件
```bash
# 从备份恢复
python backup_project.py
# 选择 "3. 从备份恢复"

# 或从Git恢复
git checkout HEAD -- <文件名>
```

## 📊 系统状态检查

使用主控制器可以随时查看系统状态：
```bash
python version_control_manager.py
```

状态信息包括：
- ✅ Git仓库状态
- 📦 备份数量
- 🔍 代码追踪状态
- ⚠️ 未提交的更改

## 💡 最佳实践

### 1. 定期备份
- 每天开始工作前创建备份
- 重要实验前创建备份
- 参数调优前创建备份

### 2. 有意义的提交信息
```bash
git commit -m "优化TimeMixer参数配置 - 提高NSE到0.85"
git commit -m "修复数据标准化bug - 影响预测精度"
git commit -m "添加新的评估指标 - MAPE和SMAPE"
```

### 3. 分支管理
- `main/master`: 稳定版本
- `development`: 日常开发
- `experiment_*`: 特定实验

### 4. 备份清理
定期清理旧备份以节省空间：
```bash
# 保留最近10个备份，删除其他
# (可以手动在backups目录中操作)
```

## 🔧 故障排除

### Git相关问题
```bash
# 如果Git命令失败
git config --global user.name "您的姓名"
git config --global user.email "您的邮箱"

# 如果分支切换失败
git stash  # 暂存更改
git checkout <分支名>
git stash pop  # 恢复更改
```

### 备份相关问题
- 确保有足够的磁盘空间
- 检查文件权限
- 避免在备份过程中修改文件

### 恢复相关问题
- 恢复前确认选择正确的版本
- 注意恢复会覆盖当前文件
- 利用临时备份功能保护当前状态

## 📞 支持

如果遇到问题：
1. 检查错误信息
2. 确认文件权限
3. 查看系统状态
4. 使用紧急备份功能保护当前工作

---

**记住**: 版本控制的目的是让您能够安全地进行实验，不用担心代码丢失或损坏。定期使用这些工具，让您的深度学习研究更加安全和高效！
