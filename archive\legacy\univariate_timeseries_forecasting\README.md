# Univariate Time Series Forecasting with TimeMixer++

这个项目提供了一个完整的单变量时间序列预测解决方案，使用PyPOTS库中的TimeMixer++模型。专门针对您的runoff数据格式进行了优化。

## 📁 项目结构

```
univariate_timeseries_forecasting/
├── data_loader.py              # 数据加载和预处理模块
├── model_config.py             # 模型配置管理
├── model_trainer.py            # 模型训练和评估
├── main_forecasting_pipeline.py # 主执行脚本
├── requirements.txt            # 依赖库列表
└── README.md                  # 项目说明文档
```

## 🚀 快速开始

### 1. 安装依赖

```bash
cd univariate_timeseries_forecasting
pip install -r requirements.txt
```

### 2. 准备数据

确保您的CSV文件格式如下：
```csv
DATA,runoff
1950/1/1,1180
1950/1/2,1140
1950/1/3,1120
1950/1/4,1200
1950/1/5,1500
1950/1/6,1700
```

### 3. 运行预测

#### 方法1：使用您的数据文件
```bash
python main_forecasting_pipeline.py --data_path your_data.csv
```

#### 方法2：使用示例数据测试
```bash
python main_forecasting_pipeline.py --use_sample
```

#### 方法3：交互式模式
```bash
python main_forecasting_pipeline.py --interactive
```

#### 方法4：演示模式（无参数）
```bash
python main_forecasting_pipeline.py
```

## 📊 预设配置

| 配置名称 | 输入长度 | 预测长度 | 训练轮数 | 适用场景 |
|---------|---------|---------|---------|---------|
| `quick_test` | 10天 | 3天 | 10轮 | 快速测试 |
| `short_term` | 14天 | 3天 | 50轮 | 短期预测 |
| `medium_term` | 30天 | 7天 | 100轮 | 中期预测（推荐） |
| `long_term` | 90天 | 30天 | 150轮 | 长期预测 |
| `daily_to_weekly` | 30天 | 7天 | 100轮 | 日到周预测 |

## 🛠 高级用法

### 自定义参数
```bash
python main_forecasting_pipeline.py \
    --data_path your_data.csv \
    --custom_steps 45 \
    --custom_pred_steps 14 \
    --custom_epochs 80
```

### 指定列名和日期格式
```bash
python main_forecasting_pipeline.py \
    --data_path your_data.csv \
    --date_column "Date" \
    --target_column "Flow" \
    --date_format "%Y-%m-%d"
```

## 📈 输出结果

训练完成后，将生成以下文件：

### 模型文件
- `trained_model.pypots` - 训练好的模型

### 结果文件夹 (`results/`)
- `evaluation_metrics.csv` - 评估指标
- `training_config.csv` - 训练配置
- `sample_predictions.csv` - 预测样本

### 可视化图表
- 实际值 vs 预测值对比图
- 多个样本的预测结果展示

## 📋 评估指标

- **MSE** (均方误差): 预测误差的平方平均值
- **RMSE** (均方根误差): MSE的平方根，与原数据同单位
- **MAE** (平均绝对误差): 预测误差绝对值的平均值
- **MAPE** (平均绝对百分比误差): 相对误差的百分比
- **R²** (决定系数): 模型解释数据变异的比例

## 🔧 模块说明

### data_loader.py
- `UnivariateTimeSeriesLoader`: 数据加载和预处理类
- 支持CSV文件读取、日期解析、数据标准化
- 自动处理缺失值和创建时间序列序列

### model_config.py
- `UnivariateTimeMixerConfig`: 单变量预测专用配置类
- `UnivariateModelFactory`: 模型创建工厂类
- 预设配置和自定义配置支持

### model_trainer.py
- `UnivariateModelTrainer`: 模型训练和评估类
- 完整的训练流程管理
- 自动评估和结果可视化

### main_forecasting_pipeline.py
- 主执行脚本，整合所有功能
- 命令行参数支持
- 交互式配置选择

## 💡 使用技巧

### 1. 选择合适的配置
- **数据量小（<1000样本）**: 使用 `short_term` 或 `quick_test`
- **数据量中等（1000-5000样本）**: 使用 `medium_term`
- **数据量大（>5000样本）**: 使用 `long_term`

### 2. 调整预测长度
- 短期预测（1-7天）: 更准确
- 中期预测（1-4周）: 平衡准确性和实用性
- 长期预测（1-3月）: 趋势预测

### 3. 优化训练时间
- 使用 `quick_test` 进行快速验证
- GPU加速：确保安装CUDA版本的PyTorch
- 减少 `epochs` 数量进行快速测试

## 🐛 常见问题

### Q1: 导入错误 "No module named 'pypots'"
```bash
pip install pypots
```

### Q2: CUDA内存不足
- 减小 `batch_size`
- 使用CPU训练：`--device cpu`

### Q3: 训练时间过长
- 使用 `quick_test` 配置
- 减少 `epochs` 数量
- 使用GPU加速

### Q4: 预测效果不好
- 增加输入序列长度 (`n_steps`)
- 调整模型复杂度 (`d_model`, `n_layers`)
- 检查数据质量和预处理

### Q5: CSV格式错误
确保CSV文件：
- 包含日期列（默认名称：DATA）
- 包含目标变量列（默认名称：runoff）
- 日期格式正确（默认：%Y/%m/%d）

## 📚 示例代码

### Python脚本中使用
```python
from data_loader import UnivariateTimeSeriesLoader
from model_config import get_univariate_configs, UnivariateModelFactory
from model_trainer import UnivariateModelTrainer

# 加载数据
loader = UnivariateTimeSeriesLoader()
data = loader.load_csv('your_data.csv')

# 创建配置
configs = get_univariate_configs()
config = configs['medium_term']

# 训练模型
trainer = UnivariateModelTrainer(config)
train_data, val_data, test_data = trainer.prepare_data('your_data.csv')
trainer.create_model()
trainer.train_model(train_data, val_data)

# 评估模型
results = trainer.evaluate_model(test_data)
trainer.plot_predictions(results)
```

## 🔄 更新日志

- **v1.0**: 初始版本，支持基本的单变量时间序列预测
- 支持TimeMixer++和TimeMixer模型
- 完整的数据预处理流程
- 多种预设配置
- 自动评估和可视化

## 📞 支持

如果您遇到问题或需要帮助：
1. 检查错误信息和常见问题部分
2. 确保所有依赖库正确安装
3. 验证数据格式是否正确
4. 尝试使用示例数据测试

## 📄 许可证

本项目基于MIT许可证开源。
